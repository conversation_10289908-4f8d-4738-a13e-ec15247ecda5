import { Pool } from 'pg';
import { UserRole, UserStatus } from '@aperion/shared';
import { config } from '../config';
import { logger } from '../../../utils/logger';
import {
  AdminUser,
  CreateUserRequest,
  UpdateUserRequest,
  UserQueryParams,
  UserStatistics
} from '../types/user';

export class UserModel {
  private readonly pool: Pool;

  constructor() {
    // Create PostgreSQL connection pool
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.database,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
      max: config.database.maxConnections,
      idleTimeoutMillis: config.database.idleTimeoutMillis,
      connectionTimeoutMillis: config.database.connectionTimeoutMillis,
    });

    // Handle pool errors
    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  // Helper method to execute queries
  async query(text: string, params?: any[]): Promise<any> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }

  /**
   * Get all users with pagination and filtering
   */
  async getUsers(params: UserQueryParams): Promise<{ users: AdminUser[]; total: number }> {
    const {
      page = 1,
      limit = 20,
      search,
      role,
      status,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = params;

    const offset = (page - 1) * limit;
    let whereConditions: string[] = [];
    let queryParams: any[] = [];
    let paramIndex = 1;

    // Build WHERE conditions
    if (search) {
      whereConditions.push(`(
        ur.first_name ILIKE $${paramIndex} OR
        ur.last_name ILIKE $${paramIndex} OR
        ur.email ILIKE $${paramIndex} OR
        ur.phone_number ILIKE $${paramIndex}
      )`);
      queryParams.push(`%${search}%`);
      paramIndex++;
    }

    if (role) {
      whereConditions.push(`ur.user_type = $${paramIndex}`);
      queryParams.push(role);
      paramIndex++;
    }

    if (status) {
      whereConditions.push(`ur.status = $${paramIndex}`);
      queryParams.push(status);
      paramIndex++;
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';
    
    // Validate sortBy to prevent SQL injection
    const allowedSortFields = ['created_at', 'updated_at', 'first_name', 'last_name', 'email', 'user_type', 'status'];
    const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
    const safeSortOrder = sortOrder === 'asc' ? 'ASC' : 'DESC';

    const query = `
      SELECT
        ur.id,
        ur.cognito_user_id as "cognitoUserId",
        ur.first_name as "firstName",
        ur.last_name as "lastName",
        ur.email,
        ur.phone_number as "phoneNumber",
        ur.user_type as "userType",
        ur.service_user_id as "serviceUserId",
        ur.status,
        ur.created_at as "createdAt",
        ur.updated_at as "updatedAt",
        -- Additional computed fields
        CASE
          WHEN ur.user_type = 'member' THEN 'member'
          WHEN ur.user_type = 'employer' THEN 'employer'
          WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
          WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
          WHEN ur.user_type = 'system_admin' THEN 'system-admin'
          ELSE ur.user_type
        END as role,
        -- Use database functions for real data
        command_center.get_user_company(ur.id) as company,
        command_center.get_user_subscription(ur.id) as subscription,
        command_center.get_user_last_activity(ur.id) as "lastActive"
      FROM shared_data.user_references ur
      ${whereClause}
      ORDER BY ur.${safeSortBy} ${safeSortOrder}
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    queryParams.push(limit, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM shared_data.user_references ur
      ${whereClause}
    `;

    try {
      const [usersResult, countResult] = await Promise.all([
        this.query(query, queryParams),
        this.query(countQuery, queryParams.slice(0, -2)) // Remove limit and offset for count
      ]);

      const users: AdminUser[] = usersResult.rows.map((row: any) => ({
        ...row,
        phone: row.phoneNumber, // Map phoneNumber from database to phone field for backward compatibility
        // Use real data from database functions, fallback to mock if null
        company: row.company ?? this.getMockCompany(row.userType),
        subscription: row.subscription ?? this.getMockSubscription(row.userType),
        lastActive: row.lastActive ?? this.generateLastActive(),
      }));

      return {
        users,
        total: parseInt(countResult.rows[0].total, 10)
      };
    } catch (error) {
      logger.error('Error fetching users:', error);
      throw new Error('Failed to fetch users');
    }
  }

  /**
   * Get user by ID
   */
  async getUserById(id: string): Promise<AdminUser | null> {
    const query = `
      SELECT
        ur.id,
        ur.cognito_user_id as "cognitoUserId",
        ur.first_name as "firstName",
        ur.last_name as "lastName",
        ur.email,
        ur.phone_number as "phoneNumber",
        ur.user_type as "userType",
        ur.service_user_id as "serviceUserId",
        ur.status,
        ur.created_at as "createdAt",
        ur.updated_at as "updatedAt",
        CASE
          WHEN ur.user_type = 'member' THEN 'member'
          WHEN ur.user_type = 'employer' THEN 'employer'
          WHEN ur.user_type = 'wellness_coach' THEN 'wellness-coach'
          WHEN ur.user_type = 'lms_creator' THEN 'content-creator'
          WHEN ur.user_type = 'system_admin' THEN 'system-admin'
          ELSE ur.user_type
        END as role
      FROM shared_data.user_references ur
      WHERE ur.id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];

      // Get real company and subscription data using database functions
      const companyQuery = 'SELECT command_center.get_user_company($1) as company';
      const subscriptionQuery = 'SELECT command_center.get_user_subscription($1) as subscription';
      const lastActiveQuery = 'SELECT command_center.get_user_last_activity($1) as last_active';

      const [companyResult, subscriptionResult, lastActiveResult] = await Promise.all([
        this.query(companyQuery, [user.id]),
        this.query(subscriptionQuery, [user.id]),
        this.query(lastActiveQuery, [user.id])
      ]);

      return {
        ...user,
        phone: user.phoneNumber, // Map phoneNumber from database to phone field for backward compatibility
        company: companyResult.rows[0]?.company || this.getMockCompany(user.userType),
        subscription: subscriptionResult.rows[0]?.subscription || this.getMockSubscription(user.userType),
        lastActive: lastActiveResult.rows[0]?.last_active || this.generateLastActive(),
      };
    } catch (error) {
      logger.error('Error fetching user by ID:', error);
      throw new Error('Failed to fetch user');
    }
  }

  /**
   * Create a new user
   */
 async createUser(userData: CreateUserRequest): Promise<AdminUser> {
  const query = `
    INSERT INTO shared_data.user_references (
      cognito_user_id,
      user_type,
      service_user_id,
      email,
      phone_number,       
      first_name,
      last_name,
      status
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
    RETURNING
      id,
      cognito_user_id as "cognitoUserId",
      first_name as "firstName",
      last_name as "lastName",
      email,
      phone_number as "phoneNumber",  
      user_type as "userType",
      service_user_id as "serviceUserId",
      status,
      created_at as "createdAt",
      updated_at as "updatedAt"
  `;
    // Use provided Cognito user ID or generate mock one for backward compatibility
    const cognitoUserId = userData.cognitoUserId;

    // Generate a proper UUID for service_user_id using PostgreSQL's gen_random_uuid()
    const serviceUserIdQuery = 'SELECT gen_random_uuid() as uuid';
    const serviceUserIdResult = await this.query(serviceUserIdQuery);
    const serviceUserId = serviceUserIdResult.rows[0].uuid;

    // Log the user creation with Cognito integration status
    logger.info('Creating user in database:', {
      email: userData.email,
      cognitoUserId,
      isRealCognito: !!userData.cognitoUserId
    });

    try {
      const result = await this.query(query, [
        cognitoUserId,
        userData.userType,
        serviceUserId,
        userData.email,
        userData.phoneNumber ?? userData.phone, // Use phoneNumber if available, fallback to phone
        userData.firstName,
        userData.lastName,
        'active'
      ]);

      const user = result.rows[0];

      // Get real company and subscription data using database functions
      const companyQuery = 'SELECT command_center.get_user_company($1) as company';
      const subscriptionQuery = 'SELECT command_center.get_user_subscription($1) as subscription';
      const lastActiveQuery = 'SELECT command_center.get_user_last_activity($1) as last_active';

      const [companyResult, subscriptionResult, lastActiveResult] = await Promise.all([
        this.query(companyQuery, [user.id]),
        this.query(subscriptionQuery, [user.id]),
        this.query(lastActiveQuery, [user.id])
      ]);

      return {
        ...user,
        role: userData.role,
        phone: user.phoneNumber, // Map phoneNumber from database to phone field for backward compatibility
        company: userData.company ?? companyResult.rows[0]?.company ?? this.getMockCompany(userData.userType),
        subscription: userData.subscription ?? subscriptionResult.rows[0]?.subscription ?? this.getMockSubscription(userData.userType),
        lastActive: lastActiveResult.rows[0]?.last_active ?? this.generateLastActive(),
      };
    } catch (error) {
      logger.error('Error creating user:', error);
      throw new Error('Failed to create user');
    }
  }

  /**
   * Update user
   */
  async updateUser(id: string, userData: UpdateUserRequest): Promise<AdminUser | null> {
    const updateFields: string[] = [];
    const queryParams: any[] = [];
    let paramIndex = 1;

    // Build dynamic update query
    if (userData.firstName) {
      updateFields.push(`first_name = $${paramIndex}`);
      queryParams.push(userData.firstName);
      paramIndex++;
    }

    if (userData.lastName) {
      updateFields.push(`last_name = $${paramIndex}`);
      queryParams.push(userData.lastName);
      paramIndex++;
    }

    if (userData.email) {
      updateFields.push(`email = $${paramIndex}`);
      queryParams.push(userData.email);
      paramIndex++;
    }

    if (userData.phoneNumber ?? userData.phone) {
      updateFields.push(`phone_number = $${paramIndex}`);
      queryParams.push(userData.phoneNumber ?? userData.phone);
      paramIndex++;
    }

    if (userData.status) {
      updateFields.push(`status = $${paramIndex}`);
      queryParams.push(userData.status);
      paramIndex++;
    }

    if (updateFields.length === 0) {
      throw new Error('No fields to update');
    }

    updateFields.push(`updated_at = NOW()`);
    queryParams.push(id);

    const query = `
      UPDATE shared_data.user_references 
      SET ${updateFields.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING
        id,
        cognito_user_id as "cognitoUserId",
        first_name as "firstName",
        last_name as "lastName",
        email,
        phone_number as "phoneNumber",
        user_type as "userType",
        service_user_id as "serviceUserId",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    try {
      const result = await this.query(query, queryParams);
      
      if (result.rows.length === 0) {
        return null;
      }

      const user = result.rows[0];

      // Get real company and subscription data using database functions
      const companyQuery = 'SELECT command_center.get_user_company($1) as company';
      const subscriptionQuery = 'SELECT command_center.get_user_subscription($1) as subscription';
      const lastActiveQuery = 'SELECT command_center.get_user_last_activity($1) as last_active';

      const [companyResult, subscriptionResult, lastActiveResult] = await Promise.all([
        this.query(companyQuery, [user.id]),
        this.query(subscriptionQuery, [user.id]),
        this.query(lastActiveQuery, [user.id])
      ]);

      return {
        ...user,
        role: this.mapUserTypeToRole(user.userType),
        phone: user.phoneNumber, // Map phoneNumber from database to phone field for backward compatibility
        company: userData.company ?? companyResult.rows[0]?.company ?? this.getMockCompany(user.userType),
        subscription: userData.subscription ?? subscriptionResult.rows[0]?.subscription ?? this.getMockSubscription(user.userType),
        lastActive: lastActiveResult.rows[0]?.last_active ?? this.generateLastActive(),
      };
    } catch (error) {
      logger.error('Error updating user:', error);
      throw new Error('Failed to update user');
    }
  }

  /**
   * Delete user (soft delete by setting status to 'deleted')
   */
  async deleteUser(id: string): Promise<boolean> {
    const query = `
      UPDATE shared_data.user_references 
      SET status = 'deleted', updated_at = NOW()
      WHERE id = $1
    `;

    try {
      const result = await this.query(query, [id]);
      return result.rowCount > 0;
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  }

  /**
   * Get user statistics
   */
  async getUserStatistics(): Promise<UserStatistics> {
    const query = `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active_users,
        COUNT(CASE WHEN status = 'suspended' THEN 1 END) as suspended_users,
        COUNT(CASE WHEN created_at >= DATE_TRUNC('month', CURRENT_DATE) THEN 1 END) as new_users_this_month,
        COUNT(CASE WHEN user_type = 'member' THEN 1 END) as member_count,
        COUNT(CASE WHEN user_type = 'employer' THEN 1 END) as employer_count,
        COUNT(CASE WHEN user_type = 'wellness_coach' THEN 1 END) as coach_count,
        COUNT(CASE WHEN user_type = 'system_admin' THEN 1 END) as admin_count
      FROM shared_data.user_references
      WHERE status != 'deleted'
    `;

    try {
      const result = await this.query(query);
      const stats = result.rows[0];

      return {
        totalUsers: parseInt(stats.total_users, 10),
        activeUsers: parseInt(stats.active_users, 10),
        suspendedUsers: parseInt(stats.suspended_users, 10),
        newUsersThisMonth: parseInt(stats.new_users_this_month, 10),
        usersByRole: {
          [UserRole.MEMBER]: parseInt(stats.member_count, 10),
          [UserRole.EMPLOYER]: parseInt(stats.employer_count, 10),
          [UserRole.WELLNESS_COACH]: parseInt(stats.coach_count, 10),
          [UserRole.SYSTEM_ADMIN]: parseInt(stats.admin_count, 10),
          [UserRole.HR_MANAGER]: 0,
          [UserRole.WELLNESS_COORDINATOR]: 0,
          [UserRole.LEARNER]: 0,
          [UserRole.CONTENT_CREATOR]: 0,
          [UserRole.LMS_ADMIN]: 0,
          [UserRole.OPERATIONS_MANAGER]: 0,
        },
        usersByStatus: {
          [UserStatus.ACTIVE]: parseInt(stats.active_users, 10),
          [UserStatus.INACTIVE]: 0,
          [UserStatus.PENDING]: 0,
          [UserStatus.SUSPENDED]: parseInt(stats.suspended_users, 10),
        }
      };
    } catch (error) {
      logger.error('Error fetching user statistics:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  // Helper methods
  private mapUserTypeToRole(userType: string): UserRole {
    switch (userType) {
      case 'member': return UserRole.MEMBER;
      case 'employer': return UserRole.EMPLOYER;
      case 'wellness_coach': return UserRole.WELLNESS_COACH;
      case 'lms_creator': return UserRole.CONTENT_CREATOR;
      case 'system_admin': return UserRole.SYSTEM_ADMIN;
      default: return UserRole.MEMBER;
    }
  }

  private getMockCompany(userType: string): string {
    switch (userType) {
      case 'member': return 'TechCorp Solutions';
      case 'employer': return 'Global Health Corp';
      case 'wellness_coach': return 'WellnessTech Inc';
      default: return 'Platform Administration';
    }
  }

  private getMockSubscription(userType: string): string | undefined {
    switch (userType) {
      case 'member': return 'Premium Health Plan';
      case 'employer': return 'Enterprise';
      default: return undefined;
    }
  }

  private generateLastActive(): string {
    const options = ['2 hours ago', '30 minutes ago', '1 hour ago', '3 hours ago', '45 minutes ago', '15 minutes ago'];
    return options[Math.floor(Math.random() * options.length)];
  }

  /**
   * Get unique companies from user data
   * This method fetches real company data from the database and includes mock companies as fallback
   */
  async getUniqueCompanies(): Promise<string[]> {
    try {
      // Define interface for database row result
      interface CompanyRow {
        company: string;
      }

      // First, try to get real companies from the database using the command_center function
      const realCompaniesQuery = `
        SELECT DISTINCT command_center.get_user_company(ur.id) as company
        FROM shared_data.user_references ur
        WHERE command_center.get_user_company(ur.id) IS NOT NULL
        ORDER BY company
      `;

      const result = await this.query(realCompaniesQuery);
      const realCompanies = result.rows
        .map((row: CompanyRow) => row.company)
        .filter((company: string) => company && company.trim() !== '');

      // Include mock companies for users without real company data
      const mockCompanies = [
        'TechCorp Solutions',
        'Global Health Corp',
        'WellnessTech Inc',
        'Mindfulness Center',
        'InnovateHealth Corp',
        'Platform Administration'
      ];

      // Combine real and mock companies, remove duplicates, and sort alphabetically
      const allCompanies = [...new Set([...realCompanies, ...mockCompanies])];
      return allCompanies.sort((a, b) => a.localeCompare(b));

    } catch (error) {
      logger.error('Error fetching unique companies:', error);

      // Fallback to mock companies if database query fails
      return [
        'TechCorp Solutions',
        'Global Health Corp',
        'WellnessTech Inc',
        'Mindfulness Center',
        'InnovateHealth Corp',
        'Platform Administration'
      ].sort((a, b) => a.localeCompare(b));
    }
  }
}
