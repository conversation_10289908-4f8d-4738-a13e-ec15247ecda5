#!/usr/bin/env node

/**
 * Aperion Health Database Migration Script
 * Single Database with Schema-based Architecture
 */

const { Client } = require('pg');
const fs = require('fs');
const path = require('path');

// Database configuration
const dbConfig = {
  host: '**************',
  user: 'postgres',
  password: 'Cloud@2025',
  database: 'AperionHealth-Dev',
  port: 5432,
  ssl: false // Set to true if SSL is required
};

// Migration files in order
const migrationFiles = [
  '00_initial_setup.sql',
  '01_member_service.sql',
  '02_employer_service.sql',
  '03_wellness_service.sql',
  '04_lms_service.sql',
  '05_command_center.sql',
  '06_sample_data.sql',
  '07_user_management_enhancements.sql',
  '08_add_phone_number_to_user_references.sql'
];

class DatabaseMigrator {
  constructor(config) {
    this.client = new Client(config);
    this.schemaPath = path.join(__dirname, '../schema');
  }

  async connect() {
    try {
      await this.client.connect();
      console.log('✅ Connected to PostgreSQL database');
    } catch (error) {
      console.error('❌ Failed to connect to database:', error.message);
      throw error;
    }
  }

  async disconnect() {
    await this.client.end();
    console.log('✅ Disconnected from database');
  }

  async executeSQL(sql, description) {
    try {
      console.log(`🔄 Executing: ${description}`);
      await this.client.query(sql);
      console.log(`✅ Completed: ${description}`);
    } catch (error) {
      console.error(`❌ Failed: ${description}`);
      console.error('Error:', error.message);
      throw error;
    }
  }

  async runMigrationFile(filename) {
    const filePath = path.join(this.schemaPath, filename);
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`Migration file not found: ${filePath}`);
    }

    const sql = fs.readFileSync(filePath, 'utf8');
    await this.executeSQL(sql, `Migration file: ${filename}`);
  }

  async createMigrationTable() {
    const sql = `
      CREATE TABLE IF NOT EXISTS public.schema_migrations (
        id SERIAL PRIMARY KEY,
        filename VARCHAR(255) NOT NULL UNIQUE,
        executed_at TIMESTAMP DEFAULT NOW(),
        checksum VARCHAR(64)
      );
    `;
    await this.executeSQL(sql, 'Creating migration tracking table');
  }

  async isMigrationExecuted(filename) {
    const result = await this.client.query(
      'SELECT filename FROM public.schema_migrations WHERE filename = $1',
      [filename]
    );
    return result.rows.length > 0;
  }

  async recordMigration(filename) {
    await this.client.query(
      'INSERT INTO public.schema_migrations (filename) VALUES ($1)',
      [filename]
    );
  }

  async runMigrations() {
    console.log('🚀 Starting database migrations...\n');

    await this.createMigrationTable();

    for (const filename of migrationFiles) {
      if (await this.isMigrationExecuted(filename)) {
        console.log(`⏭️  Skipping already executed migration: ${filename}`);
        continue;
      }

      try {
        await this.runMigrationFile(filename);
        await this.recordMigration(filename);
        console.log(`📝 Recorded migration: ${filename}\n`);
      } catch (error) {
        console.error(`💥 Migration failed: ${filename}`);
        throw error;
      }
    }

    console.log('🎉 All migrations completed successfully!');
  }

  async verifySchema() {
    console.log('\n🔍 Verifying schema...');

    const schemas = [
      'shared_data',
      'member_service', 
      'employer_service',
      'wellness_service',
      'lms_service',
      'command_center'
    ];

    for (const schema of schemas) {
      const result = await this.client.query(
        'SELECT schema_name FROM information_schema.schemata WHERE schema_name = $1',
        [schema]
      );
      
      if (result.rows.length > 0) {
        console.log(`✅ Schema exists: ${schema}`);
      } else {
        console.log(`❌ Schema missing: ${schema}`);
      }
    }

    // Count tables in each schema
    const tableCountQuery = `
      SELECT schemaname, COUNT(*) as table_count 
      FROM pg_tables 
      WHERE schemaname IN ('shared_data', 'member_service', 'employer_service', 'wellness_service', 'lms_service', 'command_center')
      GROUP BY schemaname
      ORDER BY schemaname;
    `;

    const tableResult = await this.client.query(tableCountQuery);
    console.log('\n📊 Table counts by schema:');
    tableResult.rows.forEach(row => {
      console.log(`   ${row.schemaname}: ${row.table_count} tables`);
    });
  }

  async showSampleData() {
    console.log('\n📋 Sample data verification:');

    const queries = [
      { name: 'User References', query: 'SELECT COUNT(*) FROM shared_data.user_references' },
      { name: 'Employers', query: 'SELECT COUNT(*) FROM employer_service.employers' },
      { name: 'Members', query: 'SELECT COUNT(*) FROM member_service.members' },
      { name: 'Wellness Coaches', query: 'SELECT COUNT(*) FROM wellness_service.wellness_coaches' },
      { name: 'Learning Modules', query: 'SELECT COUNT(*) FROM lms_service.learning_modules' },
      { name: 'System Admins', query: 'SELECT COUNT(*) FROM command_center.system_administrators' }
    ];

    for (const { name, query } of queries) {
      try {
        const result = await this.client.query(query);
        console.log(`   ${name}: ${result.rows[0].count} records`);
      } catch (error) {
        console.log(`   ${name}: Error - ${error.message}`);
      }
    }
  }
}

// Main execution
async function main() {
  const migrator = new DatabaseMigrator(dbConfig);

  try {
    await migrator.connect();
    await migrator.runMigrations();
    await migrator.verifySchema();
    await migrator.showSampleData();
  } catch (error) {
    console.error('\n💥 Migration process failed:', error.message);
    process.exit(1);
  } finally {
    await migrator.disconnect();
  }
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { DatabaseMigrator };
