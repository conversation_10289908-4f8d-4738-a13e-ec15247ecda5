-- =====================================================
-- EMPLOYER SERVICE SCHEMA
-- =====================================================

-- Employers Table
CREATE TABLE employer_service.employers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cognito_user_id VARCHAR(255) UNIQUE NOT NULL,
  company_name VARCHAR(200) NOT NULL,
  company_code VARCHAR(50) UNIQUE NOT NULL,
  industry VARCHAR(100),
  company_size VARCHAR(50) CHECK (company_size IN ('1-10', '11-50', '51-200', '201-500', '501-1000', '1000+')),
  address JSONB,
  contact_email VARCHAR(255),
  contact_phone VARCHAR(20),
  website VARCHAR(255),
  logo_url VARCHAR(500),
  settings JSONB DEFAULT '{"notifications": true, "auto_approve_members": false, "require_health_screening": true}',
  subscription_plan VARCHAR(50) DEFAULT 'basic' CHECK (subscription_plan IN ('basic', 'premium', 'enterprise')),
  subscription_status VARCHAR(20) DEFAULT 'active' CHECK (subscription_status IN ('active', 'inactive', 'suspended', 'cancelled')),
  billing_info JSONB DEFAULT '{}',
  max_members INTEGER DEFAULT 100,
  current_members INTEGER DEFAULT 0,
  onboarding_completed BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee Invitations Table
CREATE TABLE employer_service.employee_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employer_id UUID REFERENCES employer_service.employers(id) ON DELETE CASCADE,
  email VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  department VARCHAR(100),
  job_title VARCHAR(100),
  employee_id VARCHAR(50),
  activation_code VARCHAR(50) UNIQUE NOT NULL,
  invitation_sent_at TIMESTAMP DEFAULT NOW(),
  activation_code_used_at TIMESTAMP,
  expires_at TIMESTAMP DEFAULT (NOW() + INTERVAL '30 days'),
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'sent', 'activated', 'expired', 'cancelled')),
  created_by UUID NOT NULL, -- References employer user
  metadata JSONB DEFAULT '{}',
  reminder_sent_count INTEGER DEFAULT 0,
  last_reminder_sent_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Company Departments Table
CREATE TABLE employer_service.company_departments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employer_id UUID REFERENCES employer_service.employers(id) ON DELETE CASCADE,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  manager_email VARCHAR(255),
  budget DECIMAL(12,2),
  member_count INTEGER DEFAULT 0,
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Employee Management Table (for tracking employees post-activation)
CREATE TABLE employer_service.employees (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employer_id UUID REFERENCES employer_service.employers(id) ON DELETE CASCADE,
  member_id UUID NOT NULL, -- References member_service.members(id)
  employee_id VARCHAR(50),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) NOT NULL,
  phone VARCHAR(20),
  department_id UUID REFERENCES employer_service.company_departments(id),
  job_title VARCHAR(100),
  hire_date DATE,
  employment_status VARCHAR(20) DEFAULT 'active' CHECK (employment_status IN ('active', 'inactive', 'terminated', 'on_leave')),
  manager_id UUID, -- Self-reference to another employee
  salary_band VARCHAR(20),
  benefits_eligible BOOLEAN DEFAULT true,
  wellness_program_enrolled BOOLEAN DEFAULT false,
  last_health_screening DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Company Wellness Programs Table
CREATE TABLE employer_service.company_wellness_programs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  employer_id UUID REFERENCES employer_service.employers(id) ON DELETE CASCADE,
  program_name VARCHAR(200) NOT NULL,
  description TEXT,
  program_type VARCHAR(50) NOT NULL,
  start_date DATE NOT NULL,
  end_date DATE,
  enrollment_deadline DATE,
  max_participants INTEGER,
  current_participants INTEGER DEFAULT 0,
  incentives JSONB DEFAULT '[]',
  requirements JSONB DEFAULT '[]',
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('draft', 'active', 'completed', 'cancelled')),
  created_by UUID NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR EMPLOYER SERVICE
-- =====================================================

CREATE INDEX idx_employers_company_code ON employer_service.employers(company_code);
CREATE INDEX idx_employers_subscription ON employer_service.employers(subscription_status);
CREATE INDEX idx_employers_cognito_id ON employer_service.employers(cognito_user_id);
CREATE INDEX idx_employers_company_name ON employer_service.employers(company_name);

CREATE INDEX idx_invitations_employer ON employer_service.employee_invitations(employer_id);
CREATE INDEX idx_invitations_activation_code ON employer_service.employee_invitations(activation_code);
CREATE INDEX idx_invitations_status ON employer_service.employee_invitations(status);
CREATE INDEX idx_invitations_email ON employer_service.employee_invitations(email);
CREATE INDEX idx_invitations_expires_at ON employer_service.employee_invitations(expires_at);

CREATE INDEX idx_departments_employer ON employer_service.company_departments(employer_id);
CREATE INDEX idx_departments_status ON employer_service.company_departments(status);

CREATE INDEX idx_employees_employer ON employer_service.employees(employer_id);
CREATE INDEX idx_employees_member ON employer_service.employees(member_id);
CREATE INDEX idx_employees_department ON employer_service.employees(department_id);
CREATE INDEX idx_employees_status ON employer_service.employees(employment_status);
CREATE INDEX idx_employees_manager ON employer_service.employees(manager_id);
CREATE INDEX idx_employees_email ON employer_service.employees(email);
CREATE UNIQUE INDEX idx_employees_employer_email ON employer_service.employees(employer_id, email);

CREATE INDEX idx_wellness_programs_employer ON employer_service.company_wellness_programs(employer_id);
CREATE INDEX idx_wellness_programs_status ON employer_service.company_wellness_programs(status);
CREATE INDEX idx_wellness_programs_dates ON employer_service.company_wellness_programs(start_date, end_date);

-- =====================================================
-- TRIGGERS FOR EMPLOYER SERVICE
-- =====================================================

CREATE TRIGGER update_employers_updated_at 
  BEFORE UPDATE ON employer_service.employers 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_departments_updated_at 
  BEFORE UPDATE ON employer_service.company_departments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_employees_updated_at 
  BEFORE UPDATE ON employer_service.employees 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_wellness_programs_updated_at 
  BEFORE UPDATE ON employer_service.company_wellness_programs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
