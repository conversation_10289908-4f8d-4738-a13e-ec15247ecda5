import React, { useState, useEffect } from 'react';
import {useSearchParams, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useToast } from '@/hooks/use-toast';
import apiClient from '@/services/apiClient';
import {
  CheckCircle,
  XCircle,
  RefreshCw,
  Heart,
  Shield,
  Users,
  Activity,
  AlertTriangle,
  Mail,
  Phone
} from 'lucide-react';

// Types for verification status
type VerificationStatus = 'loading' | 'verified' | 'unverified' | 'error';

interface VerificationResponse {
  status: 'success' | 'error';
  verified: boolean;
  message: string;
  userInfo?: {
    email?: string;
    phone?: string;
    name?: string;
  };
}

const VerificationPage: React.FC = () => {
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  
  // Get token from URL params (e.g., ?token=abc123)
  const verificationUser = searchParams.get('username');
  const navigate = useNavigate();
  
  const [verificationStatus, setVerificationStatus] = useState<VerificationStatus>('loading');
  const [verificationData, setVerificationData] = useState<VerificationResponse | null>(null);

  // API call for verification using apiClient
const checkVerification = async (user?: string | null) => {
  try {
    setVerificationStatus('loading');

    const response = await apiClient.get(`/command-center/admin/users/account-confirmation?username=${user}`);
    const rawData = response.data;

    const mappedData: VerificationResponse = {
      status: rawData.success ? 'success' : 'error',
      verified: rawData.data?.verified ?? false,
      message: rawData.message || 'No message provided',
      userInfo: {
        email: rawData.data?.email,
        // You can include phone or name if available in future
      },
    };

    setVerificationData(mappedData);
    setVerificationStatus(mappedData.verified ? 'verified' : 'unverified');

    toast({
      title: mappedData.verified ? "✅ Verification Successful" : "❌ Verification Failed",
      description: mappedData.message,
      duration: 1000,
    });
     if (mappedData.verified) {
        setTimeout(() => {
          navigate('/auth/login'); // ✅ Navigate after delay
        }, 1500); // 1.5s delay so user sees the toast
      }
  } catch (error) {
    setVerificationStatus('error');
    setVerificationData({
      status: 'error',
      verified: false,
      message: 'An unexpected error occurred. Please try again.',
    });

    toast({
      title: "⚠️ Connection Error",
      description: "Unable to verify your account. Please check your connection.",
      duration: 5000,
    });
  }
};

  // Check verification on component mount
  useEffect(() => {
    checkVerification(verificationUser);
    console.log(verificationUser)
  }, [verificationUser]);


  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      <div className="min-h-screen flex flex-col lg:flex-row">
        
        {/* Left Side - Project Content */}
        <motion.div
          initial={{ opacity: 0, x: -50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.8 }}
          className="hidden lg:flex lg:w-1/2 xl:w-3/5 bg-gradient-to-br from-blue-600 via-indigo-700 to-purple-800 relative overflow-hidden"
        >
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-20 left-20 w-32 h-32 bg-white rounded-full blur-xl"></div>
            <div className="absolute bottom-32 right-20 w-48 h-48 bg-white rounded-full blur-xl"></div>
            <div className="absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-lg"></div>
          </div>

          <div className="relative z-10 flex flex-col justify-center px-12 xl:px-20 text-white">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
            >
              <div className="flex items-center mb-8">
                <div className="w-12 h-12 rounded-xl bg-white/20 backdrop-blur-sm flex items-center justify-center mr-4">
                  <Heart className="w-6 h-6 text-white" />
                </div>
                <h1 className="text-3xl font-bold">Aperion Health</h1>
              </div>

              <h2 className="text-4xl xl:text-5xl font-bold mb-6 leading-tight">
                Account Verification
                <span className="block text-blue-200">Secure & Trusted</span>
              </h2>

              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                We're verifying your account to ensure the highest level of security for your healthcare data.
              </p>

              <div className="space-y-6">
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.6, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Shield className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Secure Verification</h3>
                    <p className="text-blue-200">Advanced security protocols protect your data</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 0.8, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Users className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Trusted Platform</h3>
                    <p className="text-blue-200">Join thousands of verified healthcare users</p>
                  </div>
                </motion.div>

                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: 1.0, duration: 0.6 }}
                  className="flex items-center space-x-4"
                >
                  <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center">
                    <Activity className="w-5 h-5 text-white" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-lg">Instant Access</h3>
                    <p className="text-blue-200">Get immediate access after verification</p>
                  </div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </motion.div>

        {/* Right Side - Verification Status */}
        <div className="flex-1 lg:w-1/2 xl:w-2/5 flex flex-col">
          
          {/* Mobile Header */}
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="lg:hidden bg-gradient-to-r from-blue-600 to-indigo-700 p-6 text-white"
          >
            <div className="flex items-center justify-center">
              <div className="w-10 h-10 rounded-lg bg-white/20 backdrop-blur-sm flex items-center justify-center mr-3">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <h1 className="text-2xl font-bold">Aperion Health</h1>
            </div>
            <p className="text-center text-blue-100 mt-2 text-sm">
              Account Verification
            </p>
          </motion.div>

          <div className="flex-1 flex items-center justify-center p-4 sm:p-6 lg:p-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="w-full max-w-md"
            >
              <Card className="shadow-2xl border-0 bg-white/95 dark:bg-slate-900/95 backdrop-blur-md">
                <CardHeader className="space-y-1 text-center pb-6">
                  <AnimatePresence mode="wait">
                    {verificationStatus === 'loading' && (
                      <motion.div
                        key="loading"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        transition={{ duration: 0.5 }}
                        className="w-16 h-16 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center text-white mx-auto mb-4 shadow-lg"
                      >
                        <RefreshCw className="w-8 h-8 animate-spin" />
                      </motion.div>
                    )}
                    
                    {verificationStatus === 'verified' && (
                      <motion.div
                        key="verified"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        transition={{ duration: 0.5 }}
                        className="w-16 h-16 rounded-2xl bg-gradient-to-r from-green-500 to-emerald-600 flex items-center justify-center text-white mx-auto mb-4 shadow-lg"
                      >
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.2, duration: 0.6, type: "spring", stiffness: 200 }}
                        >
                          <CheckCircle className="w-8 h-8" />
                        </motion.div>
                      </motion.div>
                    )}
                    
                    {(verificationStatus === 'unverified' || verificationStatus === 'error') && (
                      <motion.div
                        key="error"
                        initial={{ scale: 0.8, opacity: 0 }}
                        animate={{ scale: 1, opacity: 1 }}
                        exit={{ scale: 0.8, opacity: 0 }}
                        transition={{ duration: 0.5 }}
                        className="w-16 h-16 rounded-2xl bg-gradient-to-r from-red-500 to-rose-600 flex items-center justify-center text-white mx-auto mb-4 shadow-lg"
                      >
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: [0, 1.2, 1] }}
                          transition={{ delay: 0.2, duration: 0.6 }}
                        >
                          <XCircle className="w-8 h-8" />
                        </motion.div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                  
                  <CardTitle className="text-2xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-700 dark:from-blue-400 dark:to-indigo-500">
                    {verificationStatus === 'loading' && 'Verifying Account...'}
                    {verificationStatus === 'verified' && 'Verification Successful!'}
                    {verificationStatus === 'unverified' && 'Verification Failed'}
                    {verificationStatus === 'error' && 'Connection Error'}
                  </CardTitle>
                  
                  <CardDescription className="text-slate-600 dark:text-slate-300">
                    {verificationStatus === 'loading' && 'Please wait while we verify your account'}
                    {verificationStatus === 'verified' && 'Your account is now active and ready to use'}
                    {verificationStatus === 'unverified' && 'We couldn\'t verify your account'}
                    {verificationStatus === 'error' && 'Unable to connect to verification service'}
                  </CardDescription>
                </CardHeader>

                <CardContent className="space-y-6">
                  <AnimatePresence mode="wait">
                    {verificationStatus === 'loading' && (
                      <motion.div
                        key="loading-content"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="text-center space-y-4"
                      >
                        <div className="flex justify-center">
                          <div className="flex space-x-1">
                            {[0, 1, 2].map((i) => (
                              <motion.div
                                key={i}
                                className="w-2 h-2 bg-blue-500 rounded-full"
                                animate={{
                                  scale: [1, 1.5, 1],
                                  opacity: [0.5, 1, 0.5],
                                }}
                                transition={{
                                  duration: 1.5,
                                  repeat: Infinity,
                                  delay: i * 0.2,
                                }}
                              />
                            ))}
                          </div>
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400">
                          This may take a few moments...
                        </p>
                      </motion.div>
                    )}

                    {verificationStatus === 'verified' && verificationData && (
                      <motion.div
                        key="verified-content"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-6"
                      >
                        {/* Success Alert */}
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3, duration: 0.5 }}
                        >
                          <Alert className="border-green-200 bg-green-50 dark:bg-green-900/20">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <AlertDescription className="text-green-700 dark:text-green-300 font-medium">
                              {verificationData.message}
                            </AlertDescription>
                          </Alert>
                        </motion.div>

                        {/* User Info */}
                        {verificationData.userInfo && (
                          <motion.div
                            initial={{ opacity: 0, y: 10 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: 0.5, duration: 0.5 }}
                            className="bg-slate-50 dark:bg-slate-800 rounded-lg p-4 space-y-3"
                          >
                            <h4 className="font-medium text-slate-900 dark:text-slate-100">Account Details</h4>
                            {verificationData.userInfo.name && (
                              <div className="flex items-center space-x-2 text-sm">
                                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span className="text-slate-600 dark:text-slate-400">Name:</span>
                                <span className="font-medium text-slate-900 dark:text-slate-100">
                                  {verificationData.userInfo.name}
                                </span>
                              </div>
                            )}
                            {verificationData.userInfo.email && (
                              <div className="flex items-center space-x-2 text-sm">
                                <Mail className="w-3 h-3 text-slate-500" />
                                <span className="text-slate-600 dark:text-slate-400">Email:</span>
                                <span className="font-medium text-slate-900 dark:text-slate-100">
                                  {verificationData.userInfo.email}
                                </span>
                              </div>
                            )}
                            {verificationData.userInfo.phone && (
                              <div className="flex items-center space-x-2 text-sm">
                                <Phone className="w-3 h-3 text-slate-500" />
                                <span className="text-slate-600 dark:text-slate-400">Phone:</span>
                                <span className="font-medium text-slate-900 dark:text-slate-100">
                                  {verificationData.userInfo.phone}
                                </span>
                              </div>
                            )}
                          </motion.div>
                        )}

                        {/* Success Actions */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7, duration: 0.5 }}
                          className="space-y-3"
                        >
                        </motion.div>
                      </motion.div>
                    )}

                    {(verificationStatus === 'unverified' || verificationStatus === 'error') && verificationData && (
                      <motion.div
                        key="error-content"
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        exit={{ opacity: 0, y: -20 }}
                        className="space-y-6"
                      >
                        {/* Error Alert */}
                        <motion.div
                          initial={{ opacity: 0, scale: 0.95 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: 0.3, duration: 0.5 }}
                        >
                          <Alert variant="destructive" className="border-red-200 bg-red-50 dark:bg-red-900/20">
                            <AlertTriangle className="h-4 w-4" />
                            <AlertDescription className="font-medium">
                              {verificationData.message}
                            </AlertDescription>
                          </Alert>
                        </motion.div>

                     
                        {/* Error Actions */}
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.7, duration: 0.5 }}
                          className="space-y-3"
                        >
                         
                        </motion.div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VerificationPage;
