import { Request, Response } from 'express';
import { logger } from '../utils/logger';
import { decryptData } from '../../../utils/cryptoUtils';
import { 
  CognitoIdentityProviderClient,
  AdminUpdateUserAttributesCommand,
  AdminGetUserCommand
} from '@aws-sdk/client-cognito-identity-provider';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

interface ConfirmationResult {
  success: boolean;
  message: string;
  error?: string;
  data?: {
    userId: string;
    email?: string | undefined;
    verified: boolean;
  };
}

/**
 * Confirm employee account via email link
 * GET /api/employer/confirm-account?token=<encrypted_token>
 */
export async function confirmEmployeeAccount(req: Request, res: Response): Promise<void> {
  try {
    // Get token from query parameter
    const { token } = req.query;

    if (!token || typeof token !== 'string') {
      logger.warn('Employee account confirmation attempted without token', {
        requestId: req.headers['x-request-id'],
        ip: req.ip,
      });
      
      res.status(400).json({
        success: false,
        message: 'Invalid or missing confirmation token.',
        error: 'MISSING_TOKEN'
      });
      return;
    }

    logger.info('Employee account confirmation attempt', {
      requestId: req.headers['x-request-id'],
      ip: req.ip,
      userAgent: req.get('User-Agent'),
    });

    const result = await confirmEmployeeAccountService(token);

    if (!result.success) {
      logger.warn('Employee account confirmation failed', {
        requestId: req.headers['x-request-id'],
        error: result.error,
        message: result.message,
      });

      const statusCode = result.error === 'LINK_EXPIRED' ? 410 :
                        result.error === 'USER_NOT_FOUND' ? 404 : 400;

      res.status(statusCode).json({
        success: false,
        message: result.message,
        error: result.error
      });
      return;
    }

    logger.info('Employee account confirmation successful', {
      requestId: req.headers['x-request-id'],
      userId: result.data?.userId,
      email: result.data?.email,
      verified: true
    });

    res.status(200).json({
      success: true,
      message: result.message,
      data: result.data
    });

  } catch (error: any) {
    logger.error('Error in confirmEmployeeAccount controller:', {
      error: error.message,
      stack: error.stack,
      requestId: req.headers['x-request-id'],
    });

    res.status(500).json({
      success: false,
      message: 'An unexpected error occurred during account confirmation',
      error: 'INTERNAL_SERVER_ERROR'
    });
  }
}

/**
 * Service function to handle employee account confirmation
 */
async function confirmEmployeeAccountService(token: string): Promise<ConfirmationResult> {
  try {
    // Decrypt and validate the token
    let cognitoUserSub: string;
    try {
      cognitoUserSub = decryptData(token);
      if (!cognitoUserSub) {
        return {
          success: false,
          message: 'Invalid confirmation token',
          error: 'INVALID_TOKEN'
        };
      }
    } catch (decryptError: any) {
      if (decryptError.code === 'LINK_EXPIRED') {
        return {
          success: false,
          message: 'Confirmation link has expired. Please contact your HR department for a new invitation.',
          error: 'LINK_EXPIRED'
        };
      }

      logger.error('Token decryption failed', {
        error: decryptError.message,
        code: decryptError.code
      });

      return {
        success: false,
        message: 'Invalid confirmation link',
        error: 'INVALID_TOKEN'
      };
    }

    // Get user details from Cognito using the UserSub
    let userEmail: string | undefined;
    let username: string | undefined;
    
    try {
      // First, we need to find the username by UserSub
      // Since we can't directly query by UserSub, we'll use the UserSub as the identifier
      // and try to get user details
      const getUserCommand = new AdminGetUserCommand({
        UserPoolId: process.env.USERPOOLID!,
        Username: cognitoUserSub // In our case, we might need to find the actual username
      });

      const userResponse = await cognitoClient.send(getUserCommand);
      
      if (userResponse.UserAttributes) {
        const emailAttr = userResponse.UserAttributes.find(attr => attr.Name === 'email');
        userEmail = emailAttr?.Value;
        username = userResponse.Username;
      }

    } catch (getUserError: any) {
      logger.error('Failed to get user details from Cognito:', {
        cognitoUserSub,
        error: getUserError.message
      });

      return {
        success: false,
        message: 'User not found or invalid token',
        error: 'USER_NOT_FOUND'
      };
    }

    // Update user attributes to mark email as verified and account as confirmed
    try {
      const updateCommand = new AdminUpdateUserAttributesCommand({
        UserPoolId: process.env.USERPOOLID!,
        Username: username!,
        UserAttributes: [
          { Name: 'email_verified', Value: 'true' },
          { Name: 'phone_number_verified', Value: 'true' },
        ],
      });

      await cognitoClient.send(updateCommand);

      logger.info('Successfully confirmed employee account:', {
        cognitoUserSub,
        username,
        email: userEmail
      });

      return {
        success: true,
        message: 'Account confirmed successfully! You can now log in to your account.',
        data: {
          userId: cognitoUserSub,
          email: userEmail || undefined,
          verified: true
        }
      };

    } catch (updateError: any) {
      logger.error('Failed to update user verification status:', {
        cognitoUserSub,
        username,
        error: updateError.message
      });

      return {
        success: false,
        message: 'Failed to confirm account. Please try again or contact support.',
        error: 'CONFIRMATION_FAILED'
      };
    }

  } catch (error: any) {
    logger.error('Error in confirmEmployeeAccountService:', {
      error: error.message,
      stack: error.stack
    });

    return {
      success: false,
      message: 'An unexpected error occurred during account confirmation',
      error: 'INTERNAL_SERVER_ERROR'
    };
  }
}
