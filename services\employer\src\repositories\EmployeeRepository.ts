import { Pool } from 'pg';
import { config } from '../config';
import { Employee, Department, EmployeeQueryParams } from '../types/employee';

// Database connection pool
const pool = new Pool({
  host: config.database.host,
  port: config.database.port,
  database: config.database.name,
  user: config.database.user,
  password: config.database.password,
  ssl: config.database.ssl,
  max: config.database.connectionLimit,
  idleTimeoutMillis: config.database.idleTimeout,
  connectionTimeoutMillis: config.database.connectionTimeout,
});

export class EmployeeRepository {
  private schema = config.database.schema;

  /**
   * Create a new employee record
   */
  async create(employeeData: Omit<Employee, 'id' | 'createdAt' | 'updatedAt'>): Promise<Employee> {
    const client = await pool.connect();
    
    try {
      const query = `
        INSERT INTO ${this.schema}.employees (
          employer_id, member_id, employee_id, first_name, last_name, email, phone,
          department_id, job_title, hire_date, employment_status, manager_id,
          salary_band, benefits_eligible, wellness_program_enrolled, last_health_screening
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16)
        RETURNING *
      `;

      const values = [
        employeeData.employerId,
        employeeData.memberId,
        employeeData.employeeId,
        employeeData.firstName,
        employeeData.lastName,
        employeeData.email,
        employeeData.phone,
        employeeData.departmentId,
        employeeData.jobTitle,
        employeeData.hireDate,
        employeeData.employmentStatus,
        employeeData.managerId,
        employeeData.salaryBand,
        employeeData.benefitsEligible,
        employeeData.wellnessProgramEnrolled,
        employeeData.lastHealthScreening
      ];

      const result = await client.query(query, values);
      return this.mapRowToEmployee(result.rows[0]);

    } finally {
      client.release();
    }
  }

  /**
   * Find employee by email within an employer
   */
  async findByEmail(employerId: string, email: string): Promise<Employee | null> {
    const client = await pool.connect();
    
    try {
      const query = `
        SELECT * FROM ${this.schema}.employees 
        WHERE employer_id = $1 AND email = $2
      `;

      const result = await client.query(query, [employerId, email]);
      
      if (result.rows.length === 0) {
        return null;
      }

      return this.mapRowToEmployee(result.rows[0]);

    } finally {
      client.release();
    }
  }

  /**
   * Find employees by employer with pagination and filtering
   */
  async findByEmployer(
    employerId: string, 
    params: EmployeeQueryParams = {}
  ): Promise<{ employees: Employee[]; total: number }> {
    const client = await pool.connect();
    
    try {
      const { page = 1, limit = 20, search, department, status, sortBy = 'created_at', sortOrder = 'desc' } = params;
      const offset = (page - 1) * limit;

      // Build WHERE clause
      let whereClause = 'WHERE e.employer_id = $1';
      const queryParams: any[] = [employerId];
      let paramIndex = 2;

      if (search) {
        whereClause += ` AND (e.first_name ILIKE $${paramIndex} OR e.last_name ILIKE $${paramIndex} OR e.email ILIKE $${paramIndex})`;
        queryParams.push(`%${search}%`);
        paramIndex++;
      }

      if (department) {
        whereClause += ` AND d.name = $${paramIndex}`;
        queryParams.push(department);
        paramIndex++;
      }

      if (status) {
        whereClause += ` AND e.employment_status = $${paramIndex}`;
        queryParams.push(status);
        paramIndex++;
      }

      // Count query
      const countQuery = `
        SELECT COUNT(*) as total
        FROM ${this.schema}.employees e
        LEFT JOIN ${this.schema}.company_departments d ON e.department_id = d.id
        ${whereClause}
      `;

      const countResult = await client.query(countQuery, queryParams);
      const total = parseInt(countResult.rows[0].total);

      // Data query
      const dataQuery = `
        SELECT e.*, d.name as department_name
        FROM ${this.schema}.employees e
        LEFT JOIN ${this.schema}.company_departments d ON e.department_id = d.id
        ${whereClause}
        ORDER BY e.${sortBy} ${sortOrder.toUpperCase()}
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      queryParams.push(limit, offset);
      const dataResult = await client.query(dataQuery, queryParams);

      const employees = dataResult.rows.map(row => this.mapRowToEmployee(row));

      return { employees, total };

    } finally {
      client.release();
    }
  }

  /**
   * Find or create department by name
   */
  async findOrCreateDepartment(employerId: string, departmentName: string): Promise<Department> {
    const client = await pool.connect();
    
    try {
      // First, try to find existing department
      const findQuery = `
        SELECT * FROM ${this.schema}.company_departments 
        WHERE employer_id = $1 AND name = $2
      `;

      const findResult = await client.query(findQuery, [employerId, departmentName]);
      
      if (findResult.rows.length > 0) {
        return this.mapRowToDepartment(findResult.rows[0]);
      }

      // Create new department
      const createQuery = `
        INSERT INTO ${this.schema}.company_departments (employer_id, name, status)
        VALUES ($1, $2, 'active')
        RETURNING *
      `;

      const createResult = await client.query(createQuery, [employerId, departmentName]);
      return this.mapRowToDepartment(createResult.rows[0]);

    } finally {
      client.release();
    }
  }

  /**
   * Get all departments for an employer
   */
  async getDepartments(employerId: string): Promise<Department[]> {
    const client = await pool.connect();
    
    try {
      const query = `
        SELECT * FROM ${this.schema}.company_departments 
        WHERE employer_id = $1 AND status = 'active'
        ORDER BY name
      `;

      const result = await client.query(query, [employerId]);
      return result.rows.map(row => this.mapRowToDepartment(row));

    } finally {
      client.release();
    }
  }

  /**
   * Map database row to Employee object
   */
  private mapRowToEmployee(row: any): Employee {
    return {
      id: row.id,
      employerId: row.employer_id,
      memberId: row.member_id,
      employeeId: row.employee_id,
      firstName: row.first_name,
      lastName: row.last_name,
      email: row.email,
      phone: row.phone,
      departmentId: row.department_id,
      jobTitle: row.job_title,
      hireDate: row.hire_date,
      employmentStatus: row.employment_status,
      managerId: row.manager_id,
      salaryBand: row.salary_band,
      benefitsEligible: row.benefits_eligible,
      wellnessProgramEnrolled: row.wellness_program_enrolled,
      lastHealthScreening: row.last_health_screening,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }

  /**
   * Map database row to Department object
   */
  private mapRowToDepartment(row: any): Department {
    return {
      id: row.id,
      employerId: row.employer_id,
      name: row.name,
      description: row.description,
      managerEmail: row.manager_email,
      budget: row.budget,
      memberCount: row.member_count || 0,
      status: row.status,
      createdAt: row.created_at,
      updatedAt: row.updated_at
    };
  }
}
