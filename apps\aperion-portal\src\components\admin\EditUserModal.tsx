import React, { useState, useEffect } from 'react';
import { User } from '@/services/userService';

// Define enums locally to avoid import issues
enum UserRole {
  MEMBER = 'member',
  EMPLOYER = 'employer',
  WELLNESS_COACH = 'wellness-coach',
  SYSTEM_ADMIN = 'system-admin',
}

enum UserStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  PENDING = 'pending',
  SUSPENDED = 'suspended',
}

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, User as UserIcon, Mail, Phone, Building2, CreditCard, Shield } from 'lucide-react';

// Simple form data interface
interface EditUserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  role: UserRole;
  status: UserStatus;
  company: string;
  subscription: string;
}

interface EditUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onUpdateUser: (id: string, userData: Partial<EditUserFormData>) => Promise<boolean>;
  user: User | null;
  companies: string[];
}

// Status configurations
const statusConfigs = {
  [UserStatus.ACTIVE]: {
    label: 'Active',
    color: 'bg-green-100 text-green-800',
    description: 'User has full access to the platform',
  },
  [UserStatus.INACTIVE]: {
    label: 'Inactive',
    color: 'bg-gray-100 text-gray-800',
    description: 'User account is temporarily disabled',
  },
  [UserStatus.PENDING]: {
    label: 'Pending',
    color: 'bg-yellow-100 text-yellow-800',
    description: 'User account is awaiting activation',
  },
  [UserStatus.SUSPENDED]: {
    label: 'Suspended',
    color: 'bg-red-100 text-red-800',
    description: 'User account is suspended due to policy violation',
  },
};

// Role configurations (simplified for editing)
const roleConfigs = {
  [UserRole.MEMBER]: {
    label: 'Member',
    color: 'bg-purple-100 text-purple-800',
    description: 'Individual employee with health benefits access',
  },
  [UserRole.EMPLOYER]: {
    label: 'Employer',
    color: 'bg-blue-100 text-blue-800',
    description: 'Company administrator with employee management access',
  },
  [UserRole.WELLNESS_COACH]: {
    label: 'Wellness Coach',
    color: 'bg-green-100 text-green-800',
    description: 'Health coaching professional with member access',
  },
  [UserRole.SYSTEM_ADMIN]: {
    label: 'System Admin',
    color: 'bg-red-100 text-red-800',
    description: 'Platform administrator with full system access',
  },
};

export function EditUserModal({ open, onOpenChange, onUpdateUser, user, companies }: EditUserModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: UserRole.MEMBER,
    status: UserStatus.ACTIVE,
    company: '',
    subscription: '',
  });

  // Update form when user changes
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone || '',
        role: user.role as UserRole,
        status: user.status as UserStatus,
        company: user.company || '',
        subscription: user.subscription || '',
      });
    }
  }, [user]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!user) return;

    setIsSubmitting(true);
    try {
      // Only send changed fields
      const changes: Partial<EditUserFormData> = {};

      if (formData.firstName !== user.firstName) changes.firstName = formData.firstName;
      // For employers, always set lastName to empty string; for others, check for changes
      if (formData.role === UserRole.EMPLOYER) {
        if (user.lastName !== '') changes.lastName = '';
      } else {
        if (formData.lastName !== user.lastName) changes.lastName = formData.lastName;
      }
      if (formData.email !== user.email) changes.email = formData.email;
      if (formData.phone !== (user.phone || '')) changes.phone = formData.phone || undefined;
      if (formData.role !== user.role) changes.role = formData.role;
      if (formData.status !== user.status) changes.status = formData.status;
      if (formData.company !== (user.company || '')) changes.company = formData.company || undefined;
      if (formData.subscription !== (user.subscription || '')) changes.subscription = formData.subscription || undefined;

      if (Object.keys(changes).length === 0) {
        onOpenChange(false);
        return;
      }

      const success = await onUpdateUser(user.id, changes);
      if (success) {
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error updating user:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    if (user) {
      setFormData({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone || '',
        role: user.role as UserRole,
        status: user.status as UserStatus,
        company: user.company || '',
        subscription: user.subscription || '',
      });
    }
    onOpenChange(false);
  };

  if (!user) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <UserIcon className="w-5 h-5" />
            <span>Edit User</span>
          </DialogTitle>
          <DialogDescription>
            Update user information and permissions. Changes will be applied immediately.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* User Info Header */}
          <div className="flex items-center space-x-4 p-4 bg-slate-50 dark:bg-slate-800 rounded-lg">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
              {user.role === 'employer'
                ? user.firstName.substring(0, 2).toUpperCase()
                : `${user.firstName[0]}${user.lastName[0]}`
              }
            </div>
            <div>
              <h3 className="font-medium text-slate-900 dark:text-slate-100">
                {user.role === 'employer' ? user.firstName : `${user.firstName} ${user.lastName}`}
              </h3>
              <p className="text-sm text-slate-600 dark:text-slate-400">
                User ID: {user.id}
              </p>
            </div>
          </div>

          {/* Personal Information - Conditional based on role */}
          {formData.role === UserRole.EMPLOYER ? (
            // For employers, show only Company Name field
            <div className="space-y-2">
              <Label htmlFor="firstName">Company Name *</Label>
              <Input
                id="firstName"
                placeholder="Acme Corporation"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                required
              />
            </div>
          ) : (
            // For other roles, show First Name and Last Name
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  placeholder="John"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  required
                />
              </div>
            </div>
          )}

          {/* Contact Information */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center space-x-1">
              <Mail className="w-4 h-4" />
              <span>Email Address *</span>
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center space-x-1">
              <Phone className="w-4 h-4" />
              <span>Phone Number</span>
            </Label>
            <Input
              id="phone"
              placeholder="+****************"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            />
          </div>

          {/* Role and Status */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="role" className="flex items-center space-x-1">
                <Shield className="w-4 h-4" />
                <span>Role *</span>
              </Label>
              <Select
                value={formData.role}
                onValueChange={(value) => setFormData(prev => ({ ...prev, role: value as UserRole }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(roleConfigs).map(([role, config]) => (
                    <SelectItem key={role} value={role}>
                      <div className="flex items-center space-x-2">
                        <Badge className={`text-xs ${config.color}`}>
                          {config.label}
                        </Badge>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="status">Status *</Label>
              <Select
                value={formData.status}
                onValueChange={(value) => setFormData(prev => ({ ...prev, status: value as UserStatus }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {Object.entries(statusConfigs).map(([status, config]) => (
                    <SelectItem key={status} value={status}>
                      <div className="flex items-center space-x-2">
                        <Badge className={`text-xs ${config.color}`}>
                          {config.label}
                        </Badge>
                        <span className="text-sm text-slate-600">{config.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Company and Subscription */}
          <div className="space-y-2">
            <Label htmlFor="company" className="flex items-center space-x-1">
              <Building2 className="w-4 h-4" />
              <span>Company</span>
            </Label>
            <Select
              value={formData.company}
              onValueChange={(value) => setFormData(prev => ({ ...prev, company: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select company" />
              </SelectTrigger>
              <SelectContent>
                {companies.map((company) => (
                  <SelectItem key={company} value={company}>
                    {company}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="subscription" className="flex items-center space-x-1">
              <CreditCard className="w-4 h-4" />
              <span>Subscription Plan</span>
            </Label>
            <Select
              value={formData.subscription}
              onValueChange={(value) => setFormData(prev => ({ ...prev, subscription: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select subscription" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">No Subscription</SelectItem>
                <SelectItem value="Basic Health Plan">Basic Health Plan</SelectItem>
                <SelectItem value="Premium Health Plan">Premium Health Plan</SelectItem>
                <SelectItem value="Enterprise">Enterprise</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Update User
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
