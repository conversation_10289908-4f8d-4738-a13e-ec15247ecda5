import { Router } from 'express';
import { UserManagementController } from '../controllers/UserManagementController';
import { confirmEmployeeAccount } from '../controllers/ConfirmationController';

const router = Router();
const userManagementController = new UserManagementController();

/**
 * @route GET /api/user-management/confirm-account
 * @desc Confirm employee account via email link
 * @access Public (no auth required for confirmation links)
 * @query {string} token - Encrypted confirmation token
 */
router.get('/confirm-account', confirmEmployeeAccount);

/**
 * @route POST /api/user-management/employees
 * @desc Register a new employee in Cognito User Pool
 * @access Employer only
 * @body {object} employeeData - Employee registration data
 * @body {string} employeeData.firstName - Employee's first name
 * @body {string} employeeData.lastName - Employee's last name
 * @body {string} employeeData.email - Employee's email address
 * @body {string} employeeData.phoneNumber - Employee's phone number (used as Cognito username)
 * @body {string} employeeData.department - Employee's department
 * @body {string} employeeData.role - Employee's job title/role
 * @body {string} employeeData.healthPlan - Employee's health plan
 * @returns {object} Success response with member_id (Cognito UserSub) for referential integrity
 */
router.post('/employees', userManagementController.registerEmployee);

export { router as userManagementRouter };
