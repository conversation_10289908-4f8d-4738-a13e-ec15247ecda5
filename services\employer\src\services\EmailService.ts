import { logger } from '../utils/logger';
import { sendMail } from '../../../utils/mailService';
import { encryptData } from '../../../utils/cryptoUtils';
import fs from 'fs/promises';
import ejs from 'ejs';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config();

export interface EmailInvitationParams {
  email: string;
  firstName: string;
  lastName: string;
  cognitoUserSub: string;
}

export interface EmailResult {
  success: boolean;
  message: string;
  error?: string;
}

export class EmailService {
  /**
   * Send account confirmation email invitation to new employee
   */
  async sendEmployeeInvitation(params: EmailInvitationParams): Promise<EmailResult> {
    try {
      const { email, firstName, lastName, cognitoUserSub } = params;

      logger.info('Sending employee invitation email:', {
        email,
        firstName,
        lastName,
        cognitoUserSub
      });

      // Encrypt the Cognito UserSub for the confirmation link
      const encryptedToken = encryptData(cognitoUserSub, 24); // 24 hours expiration

      // Load and render the email template
      const templatePath = path.resolve(__dirname, '../../templates/employeeInvitationEmail.ejs');
      
      let template: string;
      try {
        template = await fs.readFile(templatePath, 'utf8');
      } catch (templateError) {
        logger.error('Failed to read email template:', {
          templatePath,
          error: templateError
        });
        return {
          success: false,
          message: 'Failed to load email template',
          error: 'TEMPLATE_NOT_FOUND'
        };
      }

      // Render the template with employee data
      const htmlContent = ejs.render(template, {
        firstName,
        lastName,
        confirmationLink: `${process.env.FRONTEND_URL}/employee/confirm-account?token=${encryptedToken}`,
        companyName: process.env.COMPANY_NAME || 'Your Company'
      });

      // Send the email
      const subject = `Welcome to ${process.env.COMPANY_NAME || 'Our Platform'} - Please Confirm Your Account`;
      const emailSent = await sendMail(htmlContent, email, subject);

      if (emailSent) {
        logger.info('Employee invitation email sent successfully:', {
          email,
          firstName,
          lastName
        });

        return {
          success: true,
          message: 'Employee invitation email sent successfully'
        };
      } else {
        logger.error('Failed to send employee invitation email:', {
          email,
          firstName,
          lastName
        });

        return {
          success: false,
          message: 'Failed to send email',
          error: 'EMAIL_SEND_FAILED'
        };
      }

    } catch (error: any) {
      logger.error('Error in EmailService.sendEmployeeInvitation:', {
        email: params.email,
        error: error.message,
        stack: error.stack
      });

      return {
        success: false,
        message: 'An unexpected error occurred while sending email',
        error: 'INTERNAL_ERROR'
      };
    }
  }

  /**
   * Resend invitation email for an existing employee
   */
  async resendEmployeeInvitation(params: EmailInvitationParams): Promise<EmailResult> {
    logger.info('Resending employee invitation email:', {
      email: params.email,
      firstName: params.firstName,
      lastName: params.lastName
    });

    // Use the same logic as initial invitation
    return this.sendEmployeeInvitation(params);
  }
}
