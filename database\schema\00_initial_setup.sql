-- =====================================================
-- Aperion Health Database Schema - Single Database Design
-- Database: AperionHealth-Dev
-- Host: **************
-- =====================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "btree_gin";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create schemas for logical separation
CREATE SCHEMA IF NOT EXISTS member_service;
CREATE SCHEMA IF NOT EXISTS employer_service;
CREATE SCHEMA IF NOT EXISTS wellness_service;
CREATE SCHEMA IF NOT EXISTS lms_service;
CREATE SCHEMA IF NOT EXISTS command_center;
CREATE SCHEMA IF NOT EXISTS shared_data;

-- Set default search path
SET search_path TO shared_data, member_service, employer_service, wellness_service, lms_service, command_center, public;

-- =====================================================
-- SHARED DATA SCHEMA
-- =====================================================

-- User References Table (Central user registry)
CREATE TABLE shared_data.user_references (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cognito_user_id VARCHAR(255) UNIQUE NOT NULL,
  user_type VARCHAR(50) NOT NULL CHECK (user_type IN ('member', 'employer', 'wellness_coach', 'lms_creator', 'system_admin')),
  service_user_id UUID NOT NULL,
  email VARCHAR(255) NOT NULL,
  first_name VARCHAR(100),
  last_name VARCHAR(100),
  status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended', 'deleted')),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Cross-Service Events Table (Event sourcing for microservices)
CREATE TABLE shared_data.cross_service_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(100) NOT NULL,
  source_service VARCHAR(50) NOT NULL,
  target_service VARCHAR(50),
  entity_id UUID NOT NULL,
  payload JSONB NOT NULL,
  status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'failed', 'retrying')),
  processed_at TIMESTAMP,
  retry_count INTEGER DEFAULT 0,
  error_message TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Audit Log Table (System-wide audit trail)
CREATE TABLE shared_data.audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID,
  user_type VARCHAR(50),
  action VARCHAR(100) NOT NULL,
  entity_type VARCHAR(50) NOT NULL,
  entity_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);

-- =====================================================
-- INDEXES FOR SHARED DATA
-- =====================================================

CREATE INDEX idx_user_references_cognito_id ON shared_data.user_references(cognito_user_id);
CREATE INDEX idx_user_references_type ON shared_data.user_references(user_type);
CREATE INDEX idx_user_references_email ON shared_data.user_references(email);
CREATE INDEX idx_user_references_status ON shared_data.user_references(status);

CREATE INDEX idx_events_type_status ON shared_data.cross_service_events(event_type, status);
CREATE INDEX idx_events_target_service ON shared_data.cross_service_events(target_service);
CREATE INDEX idx_events_entity ON shared_data.cross_service_events(entity_id);
CREATE INDEX idx_events_created_at ON shared_data.cross_service_events(created_at);

CREATE INDEX idx_audit_logs_user ON shared_data.audit_logs(user_id, user_type);
CREATE INDEX idx_audit_logs_entity ON shared_data.audit_logs(entity_type, entity_id);
CREATE INDEX idx_audit_logs_action ON shared_data.audit_logs(action);
CREATE INDEX idx_audit_logs_created_at ON shared_data.audit_logs(created_at);

-- =====================================================
-- TRIGGERS FOR UPDATED_AT
-- =====================================================

CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_references_updated_at 
  BEFORE UPDATE ON shared_data.user_references 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
