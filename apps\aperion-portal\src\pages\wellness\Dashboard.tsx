import React from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import {
  Calendar,
  UserPlus,
  MessageSquare,
  Users,
  TrendingUp,
  TrendingDown,
  Clock,
  CheckCircle,
  AlertCircle,
  BarChart3,
  Video,
  MapPin,
  ArrowUpRight,
  ArrowDownRight,
  ChevronRight
} from 'lucide-react'

const WellnessDashboard = () => {
  const navigate = useNavigate()
  const { user } = useAuth()

  // Mock data for the dashboard - in a real app, this would come from the API
  const todaySessionsCount = 5
  const pendingMessagesCount = 3
  const memberCount = 128
  const sessionCompletionRate = 92
  const memberEngagementRate = 78

  // Performance indicators
  const performanceMetrics = [
    {
      name: 'Member Retention',
      value: '94%',
      change: '+2.5%',
      trend: 'up',
    },
    {
      name: 'Goals Completion',
      value: '68%',
      change: '+5.1%',
      trend: 'up',
    },
    {
      name: 'Session No-Shows',
      value: '8.2%',
      change: '-1.3%',
      trend: 'up',
    },
    {
      name: 'Avg. Response Time',
      value: '3.5h',
      change: '-0.8h',
      trend: 'up',
    },
  ]

  // Recent members data
  const recentMembers = [
    {
      id: 1,
      name: 'Alex Johnson',
      status: 'Active',
      lastActivity: '1 hour ago',
      goalsProgress: 75,
      initials: 'AJ',
    },
    {
      id: 2,
      name: 'Sarah Williams',
      status: 'Attention Needed',
      lastActivity: '3 days ago',
      goalsProgress: 45,
      initials: 'SW',
    },
    {
      id: 3,
      name: 'Mike Chen',
      status: 'New',
      lastActivity: '2 hours ago',
      goalsProgress: 20,
      initials: 'MC',
    },
    {
      id: 4,
      name: 'Emma Davis',
      status: 'Active',
      lastActivity: '30 minutes ago',
      goalsProgress: 90,
      initials: 'ED',
    },
  ]

  // Upcoming sessions
  const upcomingSessions = [
    {
      id: 1,
      memberName: 'Alex Johnson',
      memberInitials: 'AJ',
      time: '2:00 PM',
      date: 'Today',
      type: 'Virtual',
      sessionTitle: 'Nutrition Planning',
    },
    {
      id: 2,
      memberName: 'Sarah Williams',
      memberInitials: 'SW',
      time: '3:30 PM',
      date: 'Today',
      type: 'In-person',
      sessionTitle: 'Fitness Assessment',
    },
    {
      id: 3,
      memberName: 'Mike Chen',
      memberInitials: 'MC',
      time: '10:00 AM',
      date: 'Tomorrow',
      type: 'Virtual',
      sessionTitle: 'Wellness Check-in',
    },
    {
      id: 4,
      memberName: 'Emma Davis',
      memberInitials: 'ED',
      time: '1:00 PM',
      date: 'Tomorrow',
      type: 'In-person',
      sessionTitle: 'Goal Review',
    },
  ]

  // Tasks and follow-ups
  const tasks = [
    {
      id: 1,
      title: 'Follow up with Sarah Williams',
      priority: 'High',
      dueDate: 'Today',
      type: 'follow-up',
    },
    {
      id: 2,
      title: 'Review Mike Chen\'s wellness plan',
      priority: 'Medium',
      dueDate: 'Tomorrow',
      type: 'review',
    },
    {
      id: 3,
      title: 'Prepare nutrition materials for Alex',
      priority: 'Low',
      dueDate: 'This week',
      type: 'preparation',
    },
    {
      id: 4,
      title: 'Update Emma\'s goal tracking',
      priority: 'Medium',
      dueDate: 'Today',
      type: 'update',
    },
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'Attention Needed':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'New':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
      case 'Medium':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300'
      case 'Low':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case 'High':
        return <AlertCircle className="h-4 w-4" />
      case 'Medium':
        return <Clock className="h-4 w-4" />
      case 'Low':
        return <CheckCircle className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  return (
    <div className="p-6 space-y-6 max-w-7xl mx-auto">
      {/* Welcome message and overview */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome, {user?.firstName || 'Dr. Sarah'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Here's what's happening with your members today.
          </p>
        </div>
        <div className="flex space-x-3">
          <Button
            variant="secondary"
            onClick={() => navigate('/wellness/sessions')}
          >
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Session
          </Button>
          <Button
            onClick={() => navigate('/wellness/members')}
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Add Member
          </Button>
        </div>
      </div>

      {/* Quick stats cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Today's Sessions */}
        <Card className="bg-white dark:bg-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Today's Sessions</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center">
              <div className="flex-1">
                <div className="flex items-baseline">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{todaySessionsCount}</h3>
                  <Badge className="ml-2 bg-teal-50 text-teal-700 border-teal-100 dark:bg-teal-900/30 dark:text-teal-300 dark:border-teal-800 text-xs">
                    4 Completed
                  </Badge>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">1 upcoming in 30 minutes</p>
              </div>
              <div className="h-10 w-10 bg-teal-50 rounded-full flex items-center justify-center dark:bg-teal-900/30">
                <Calendar className="h-5 w-5 text-teal-600 dark:text-teal-300" />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full mt-3 text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 hover:bg-teal-50 dark:hover:bg-teal-900/30"
              onClick={() => navigate('/wellness/sessions')}
            >
              View Schedule
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardContent>
        </Card>

        {/* Pending Messages */}
        <Card className="bg-white dark:bg-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Pending Messages</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center">
              <div className="flex-1">
                <div className="flex items-baseline">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{pendingMessagesCount}</h3>
                  <Badge className="ml-2 bg-amber-50 text-amber-700 border-amber-100 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800 text-xs">
                    1 Urgent
                  </Badge>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">Oldest: 4 hours ago</p>
              </div>
              <div className="h-10 w-10 bg-amber-50 rounded-full flex items-center justify-center dark:bg-amber-900/30">
                <MessageSquare className="h-5 w-5 text-amber-600 dark:text-amber-300" />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full mt-3 text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300 hover:bg-amber-50 dark:hover:bg-amber-900/30"
              onClick={() => navigate('/wellness/messages')}
            >
              View Messages
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardContent>
        </Card>

        {/* Total Members */}
        <Card className="bg-white dark:bg-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Total Members</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center">
              <div className="flex-1">
                <div className="flex items-baseline">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{memberCount}</h3>
                  <span className="ml-2 text-xs text-green-600 dark:text-green-400 flex items-center">
                    <ArrowUpRight className="h-3 w-3 mr-1" />
                    +4 this week
                  </span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">72 active care plans</p>
              </div>
              <div className="h-10 w-10 bg-indigo-50 rounded-full flex items-center justify-center dark:bg-indigo-900/30">
                <Users className="h-5 w-5 text-indigo-600 dark:text-indigo-300" />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full mt-3 text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 hover:bg-indigo-50 dark:hover:bg-indigo-900/30"
              onClick={() => navigate('/wellness/members')}
            >
              View Members
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardContent>
        </Card>

        {/* Performance */}
        <Card className="bg-white dark:bg-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500 dark:text-gray-400">Performance</CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="flex items-center">
              <div className="flex-1">
                <div className="flex items-baseline">
                  <h3 className="text-2xl font-bold text-gray-900 dark:text-white">{sessionCompletionRate}%</h3>
                  <span className="ml-2 text-xs text-gray-600 dark:text-gray-400">Session completion</span>
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">{memberEngagementRate}% member engagement</p>
              </div>
              <div className="h-10 w-10 bg-purple-50 rounded-full flex items-center justify-center dark:bg-purple-900/30">
                <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-300" />
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="w-full mt-3 text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 hover:bg-purple-50 dark:hover:bg-purple-900/30"
              onClick={() => navigate('/wellness/reports')}
            >
              View Reports
              <ChevronRight className="h-4 w-4 ml-1" />
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Main content grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Left column - Recent Members */}
        <div className="lg:col-span-1 space-y-4">
          <Card className="bg-white dark:bg-gray-800">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base font-semibold">Recent Members</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-600 dark:text-gray-400"
                  onClick={() => navigate('/wellness/members')}
                >
                  View all
                </Button>
              </div>
              <CardDescription className="text-sm">Recent activity and progress</CardDescription>
            </CardHeader>
            <CardContent className="space-y-5">
              {recentMembers.map((member) => (
                <div
                  key={member.id}
                  className="flex items-center space-x-4 p-3 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors cursor-pointer"
                  onClick={() => navigate(`/wellness/members/${member.id}`)}
                >
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                      {member.initials}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 dark:text-white truncate">
                      {member.name}
                    </p>
                    <div className="flex items-center mt-1">
                      <Badge
                        className={`mr-2 px-1.5 py-0.5 text-xs ${
                          member.status === 'Active'
                            ? 'bg-green-50 text-green-700 border-green-100 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800'
                            : member.status === 'Attention Needed'
                            ? 'bg-red-50 text-red-700 border-red-100 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800'
                            : 'bg-blue-50 text-blue-700 border-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800'
                        }`}
                      >
                        {member.status}
                      </Badge>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {member.lastActivity}
                      </span>
                    </div>
                    <div className="mt-2">
                      <div className="flex items-center text-xs mb-1">
                        <span className="text-gray-600 dark:text-gray-400 mr-1">Goals Progress</span>
                        <span className="font-medium text-gray-900 dark:text-white">{member.goalsProgress}%</span>
                      </div>
                      <Progress value={member.goalsProgress} className="h-1.5" />
                    </div>
                  </div>
                  <ChevronRight className="h-5 w-5 text-gray-400" />
                </div>
              ))}
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base font-semibold">Performance Metrics</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-600 dark:text-gray-400"
                  onClick={() => navigate('/wellness/reports')}
                >
                  All metrics
                </Button>
              </div>
              <CardDescription className="text-sm">Current coach performance</CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-2 gap-4">
              {performanceMetrics.map((metric, index) => (
                <div key={index} className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <p className="text-xs text-gray-500 dark:text-gray-400">{metric.name}</p>
                  <div className="flex items-center mt-1">
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">{metric.value}</p>
                    <span className={`ml-2 text-xs flex items-center ${
                      (metric.trend === 'up' && metric.name !== 'Session No-Shows') ||
                      (metric.trend === 'down' && metric.name === 'Session No-Shows')
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {metric.trend === 'up' ? (
                        <ArrowUpRight className="h-3 w-3 mr-1" />
                      ) : (
                        <ArrowDownRight className="h-3 w-3 mr-1" />
                      )}
                      {metric.change}
                    </span>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Middle and right columns - Today's sessions and Tasks */}
        <div className="lg:col-span-2 space-y-4">

          <Card className="bg-white dark:bg-gray-800">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base font-semibold">Upcoming Sessions</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-600 dark:text-gray-400"
                  onClick={() => navigate('/wellness/sessions')}
                >
                  Full schedule
                </Button>
              </div>
              <CardDescription className="text-sm">Your next coaching sessions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="divide-y divide-gray-200 dark:divide-gray-700">
                {upcomingSessions.map((session) => (
                  <div
                    key={session.id}
                    className="py-3 flex items-start space-x-4 hover:bg-gray-50 dark:hover:bg-gray-700/50 rounded-lg transition-colors p-3 cursor-pointer"
                    onClick={() => navigate(`/wellness/sessions/${session.id}`)}
                  >
                    <div className="min-w-[72px] text-center p-2 rounded-lg bg-teal-50 dark:bg-teal-900/20">
                      <p className="text-xs font-medium text-teal-800 dark:text-teal-300">{session.date}</p>
                      <p className="text-sm font-bold text-teal-900 dark:text-teal-200">{session.time}</p>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">{session.sessionTitle}</p>
                        <Badge
                          className="ml-2 px-1.5 py-0.5 text-xs bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700"
                        >
                          {session.type}
                        </Badge>
                      </div>
                      <div className="flex items-center mt-1">
                        <Avatar className="h-5 w-5 mr-1">
                          <AvatarFallback className="text-xs bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300">
                            {session.memberInitials}
                          </AvatarFallback>
                        </Avatar>
                        <p className="text-xs text-gray-600 dark:text-gray-400">{session.memberName}</p>
                      </div>
                    </div>
                    <Button size="sm" variant="outline">
                      <Calendar className="h-4 w-4 mr-1" />
                      Join
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-white dark:bg-gray-800">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-center">
                <CardTitle className="text-base font-semibold">Tasks & Follow-ups</CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  className="text-xs text-gray-600 dark:text-gray-400"
                  onClick={() => navigate('/wellness/plans')}
                >
                  All tasks
                </Button>
              </div>
              <CardDescription className="text-sm">Tasks that need your attention</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {tasks.map((task) => (
                  <div
                    key={task.id}
                    className="p-3 rounded-lg border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors flex items-center cursor-pointer"
                    onClick={() => {/* Navigate to task detail */}}
                  >
                    <div className="mr-3">
                      <div className={`h-8 w-8 rounded-full flex items-center justify-center ${
                        task.priority === 'High'
                          ? 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
                          : task.priority === 'Medium'
                          ? 'bg-amber-100 text-amber-600 dark:bg-amber-900/30 dark:text-amber-400'
                          : 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                      }`}>
                        {task.priority === 'High' ? (
                          <AlertCircle className="h-4 w-4" />
                        ) : task.priority === 'Medium' ? (
                          <Clock className="h-4 w-4" />
                        ) : (
                          <CheckCircle className="h-4 w-4" />
                        )}
                      </div>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900 dark:text-white">{task.title}</p>
                      <div className="flex items-center mt-1">
                        <Badge
                          className={`px-1.5 py-0.5 text-xs ${
                            task.priority === 'High'
                              ? 'bg-red-50 text-red-700 border-red-100 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800'
                              : task.priority === 'Medium'
                              ? 'bg-amber-50 text-amber-700 border-amber-100 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800'
                              : 'bg-blue-50 text-blue-700 border-blue-100 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800'
                          }`}
                        >
                          {task.priority.charAt(0).toUpperCase() + task.priority.slice(1)}
                        </Badge>
                        <span className="text-xs text-gray-500 dark:text-gray-400 ml-2">
                          Due: {task.dueDate}
                        </span>
                      </div>
                    </div>
                    <Button size="sm" variant="ghost">
                      <CheckCircle className="h-4 w-4" />
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

export default WellnessDashboard