# =====================================================
# APERION HEALTH - GITIGNORE CONFIGURATION
# =====================================================

# =====================================================
# ENVIRONMENT & SECRETS (CRITICAL - NEVER COMMIT)
# =====================================================

# Environment files with real secrets
.env
.env.local
.env.development
.env.test
.env.production
.env.staging
.env.*.local

# Service-specific environment files
services/*/.env
packages/*/.env
apps/*/.env

# Database credentials and connection strings
database.config.js
db-config.json
connection-strings.txt

# API keys and secrets
secrets/
keys/
certificates/
*.pem
*.key
*.crt
*.p12
*.pfx

# AWS credentials
.aws/
aws-credentials.json

# JWT secrets and tokens
jwt-secret.txt
*.jwt
auth-tokens.json

# =====================================================
# NODE.JS & NPM
# =====================================================

# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Package manager lock files (keep package-lock.json, ignore others)
yarn.lock
pnpm-lock.yaml

# NPM cache
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
.eslintcache.*

# Optional stylelint cache
.stylelintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# =====================================================
# BUILD OUTPUTS & COMPILED CODE
# =====================================================

# TypeScript compiled output
dist/
build/
# lib/ - REMOVED: Too broad, was ignoring source code in src/lib/
# Only ignore lib/ in root and package directories, not source directories
/lib/
packages/*/lib/
services/*/lib/
# apps/*/lib/ - REMOVED: This was ignoring src/lib/ directories in apps
# Only ignore build output lib directories in apps, not source lib directories
apps/*/dist/lib/
apps/*/build/lib/
out/
*.tsbuildinfo

# Webpack bundles
bundle.js
bundle.*.js
*.bundle.js

# Rollup bundles
rollup.config.*.js

# Vite build output
dist-ssr/
*.local

# Next.js build output
.next/
out/

# Nuxt.js build / generate output
.nuxt/
.output/

# Gatsby files
.cache/
public/

# Storybook build outputs
.out/
.storybook-out/
storybook-static/

# Parcel cache
.cache/
.parcel-cache/

# =====================================================
# TESTING & COVERAGE
# =====================================================

# Test coverage
coverage/
*.lcov
.nyc_output/
.coverage/

# Jest cache
.jest/

# Playwright
test-results/
playwright-report/
playwright/.cache/

# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Test databases
test.db
test.sqlite
*.test.db

# =====================================================
# LOGS & RUNTIME DATA
# =====================================================

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov/

# Grunt intermediate storage
.grunt/

# =====================================================
# DATABASE & DATA FILES
# =====================================================

# Local databases
*.sqlite
*.sqlite3
*.db
*.db3

# Database dumps and backups
*.sql.gz
*.dump
*.backup
*.bak
database-backup-*
db-dump-*

# Migration files (keep schema, ignore data)
database/data/
database/backups/
database/dumps/

# =====================================================
# DEVELOPMENT TOOLS & IDEs
# =====================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo
*~
.vim/

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# =====================================================
# OPERATING SYSTEM FILES
# =====================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
.AppleDouble
.LSOverride
Icon

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =====================================================
# DOCKER & CONTAINERIZATION
# =====================================================

# Docker
.dockerignore
docker-compose.override.yml
docker-compose.*.yml
!docker-compose.yml
!docker-compose.example.yml

# Container data
.docker/
docker-data/

# =====================================================
# CLOUD & DEPLOYMENT
# =====================================================

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
terraform.tfvars.json

# Kubernetes
*.kubeconfig
kube-config
k8s-secrets.yaml

# Serverless
.serverless/
serverless.yml.backup

# Vercel
.vercel/

# Netlify
.netlify/

# =====================================================
# UPLOADS & USER CONTENT
# =====================================================

# File uploads
uploads/
upload/
files/
media/
static/uploads/
public/uploads/

# User generated content
user-content/
attachments/

# =====================================================
# TEMPORARY & CACHE FILES
# =====================================================

# Temporary folders
tmp/
temp/
.tmp/
.temp/

# Cache directories
.cache/
cache/
.sass-cache/

# =====================================================
# MONITORING & ANALYTICS
# =====================================================

# Error tracking
.sentry/
sentry.properties

# Analytics
.analytics/
analytics-data/

# Performance monitoring
.newrelic/
newrelic.js

# =====================================================
# DOCUMENTATION BUILD
# =====================================================

# Generated documentation
docs/build/
docs/dist/
_site/
.jekyll-cache/
.jekyll-metadata

# =====================================================
# MISCELLANEOUS
# =====================================================

# Backup files
*.backup
*.bak
*.orig
*.rej

# Archive files
*.zip
*.tar.gz
*.rar
*.7z

# Lock files for editors
.#*
*#

# Temporary files
*.tmp
*.temp

# Generated files
generated/
auto-generated/

# Local configuration overrides
local.config.js
local.settings.json

# =====================================================
# APERION HEALTH SPECIFIC
# =====================================================

# Service-specific builds
services/*/dist/
services/*/build/
packages/*/dist/
packages/*/build/
apps/*/dist/
apps/*/build/

# Generated API documentation
api-docs/
swagger-docs/

# Health check endpoints data
health-check-data/

# Service logs
services/*/logs/
packages/*/logs/
apps/*/logs/

# Database migration artifacts
database/migration-artifacts/
database/temp/

# Generated configuration files
config/generated/
auto-config/

# Documentation files (exclude all .md files)
*.md
docs/*.md

# =====================================================
# ALLOW SPECIFIC FILES (OVERRIDE ABOVE RULES)
# =====================================================

# Keep example and template files
!.env.example
!.env.template
!config.example.js
!database.example.js

# Keep important documentation
!README.md
!CHANGELOG.md
!LICENSE
!CONTRIBUTING.md

# Keep configuration templates
!docker-compose.example.yml
!kubernetes.example.yaml

# Keep sample data (not real data)
!database/sample-data/
!database/schema/
package-lock.json

# Keep development docs

added_features/
package-lock.json
package-lock.json
