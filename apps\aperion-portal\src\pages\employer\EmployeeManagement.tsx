import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Search, Download, Upload, RefreshCw, MoreHorizontal, Edit, Trash2, UserPlus, Mail, Phone, Loader2 } from "lucide-react";
import { employeeService, Employee, Department, HealthPlan, CreateEmployeeRequest } from '@/services/employeeService';

// Sample employee data
const employees = [
  { id: 1, firstName: "John", lastName: "Doe", email: "<EMAIL>", department: "Engineering", role: "Software Engineer", status: "Active", dateHired: "2021-05-12", healthPlan: "Premium", wellnessScore: 82 },
  { id: 2, firstName: "Jane", lastName: "Smith", email: "<EMAIL>", department: "Marketing", role: "Marketing Manager", status: "Active", dateHired: "2020-11-03", healthPlan: "Standard", wellnessScore: 76 },
  { id: 3, firstName: "Robert", lastName: "Johnson", email: "<EMAIL>", department: "Finance", role: "Financial Analyst", status: "Active", dateHired: "2022-01-15", healthPlan: "Premium", wellnessScore: 68 },
  { id: 4, firstName: "Sarah", lastName: "Williams", email: "<EMAIL>", department: "HR", role: "HR Specialist", status: "Inactive", dateHired: "2019-08-22", healthPlan: "Basic", wellnessScore: 45 },
  { id: 5, firstName: "Michael", lastName: "Brown", email: "<EMAIL>", department: "Sales", role: "Sales Representative", status: "Active", dateHired: "2021-10-05", healthPlan: "Standard", wellnessScore: 79 },
  { id: 6, firstName: "Emily", lastName: "Davis", email: "<EMAIL>", department: "Customer Success", role: "Customer Support", status: "Active", dateHired: "2022-03-18", healthPlan: "Standard", wellnessScore: 71 },
  { id: 7, firstName: "David", lastName: "Miller", email: "<EMAIL>", department: "Engineering", role: "System Administrator", status: "Active", dateHired: "2020-07-09", healthPlan: "Premium", wellnessScore: 88 },
  { id: 8, firstName: "Lisa", lastName: "Wilson", email: "<EMAIL>", department: "Marketing", role: "Content Creator", status: "On Leave", dateHired: "2021-02-14", healthPlan: "Basic", wellnessScore: 62 },
];

// Sample pending enrollments
const pendingEnrollments = [
  { id: 101, firstName: "Thomas", lastName: "Anderson", email: "<EMAIL>", department: "Engineering", role: "Frontend Developer", status: "Pending", inviteSent: "2023-05-10" },
  { id: 102, firstName: "Jessica", lastName: "Taylor", email: "<EMAIL>", department: "Product", role: "Product Manager", status: "Pending", inviteSent: "2023-05-12" },
];

// Departments for filtering
const departments = ["All Departments", "Engineering", "Marketing", "Finance", "HR", "Sales", "Customer Success", "Product"];

// Health plans for filtering
const healthPlans = ["All Plans", "Basic", "Standard", "Premium"];

const EmployeeManagement = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDepartment, setSelectedDepartment] = useState("All Departments");
  const [selectedHealthPlan, setSelectedHealthPlan] = useState("All Plans");
  const [selectedStatus, setSelectedStatus] = useState("All Statuses");
  const [selectedEmployees, setSelectedEmployees] = useState<number[]>([]);
  const [activeTab, setActiveTab] = useState("current");
  const [isAddEmployeeOpen, setIsAddEmployeeOpen] = useState(false);

  // API data state
  const [apiEmployees, setApiEmployees] = useState<Employee[]>([]);
  const [apiDepartments, setApiDepartments] = useState<Department[]>([]);
  const [apiHealthPlans, setApiHealthPlans] = useState<HealthPlan[]>([]);

  // Loading states
  const [isLoading, setIsLoading] = useState(false);
  const [isCreatingEmployee, setIsCreatingEmployee] = useState(false);
  const [loadingDepartments, setLoadingDepartments] = useState(false);
  const [loadingHealthPlans, setLoadingHealthPlans] = useState(false);

  // Form state for Add Employee
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phoneNumber: "",
    department: "",
    role: "",
    healthPlan: ""
  });

  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    firstName: "",
    email: "",
    phoneNumber: "",
    department: "",
    role: "",
    healthPlan: ""
  });

  // Load departments and health plans on component mount
  useEffect(() => {
    loadDepartments();
    loadHealthPlans();
  }, []);

  // Load departments from API
  const loadDepartments = async () => {
    setLoadingDepartments(true);
    try {
      const response = await employeeService.getDepartments();
      if (response.success && response.data) {
        setApiDepartments(response.data);
      }
    } catch (error) {
      console.error('Failed to load departments:', error);
    } finally {
      setLoadingDepartments(false);
    }
  };

  // Load health plans from API
  const loadHealthPlans = async () => {
    setLoadingHealthPlans(true);
    try {
      const response = await employeeService.getHealthPlans();
      if (response.success && response.data) {
        setApiHealthPlans(response.data);
      }
    } catch (error) {
      console.error('Failed to load health plans:', error);
    } finally {
      setLoadingHealthPlans(false);
    }
  };

  // Load employees from API
  const loadEmployees = async () => {
    setIsLoading(true);
    try {
      const response = await employeeService.getEmployees();
      if (response.success && response.data) {
        setApiEmployees(response.data);
      }
    } catch (error) {
      console.error('Failed to load employees:', error);
    } finally {
      setIsLoading(false);
    }
  };
  
  // Filter employees based on search and filters
  const filteredEmployees = employees.filter(employee => {
    const matchesSearch = 
      employee.firstName.toLowerCase().includes(searchQuery.toLowerCase()) || 
      employee.lastName.toLowerCase().includes(searchQuery.toLowerCase()) || 
      employee.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.department.toLowerCase().includes(searchQuery.toLowerCase()) ||
      employee.role.toLowerCase().includes(searchQuery.toLowerCase());
      
    const matchesDepartment = selectedDepartment === "All Departments" || employee.department === selectedDepartment;
    const matchesHealthPlan = selectedHealthPlan === "All Plans" || employee.healthPlan === selectedHealthPlan;
    const matchesStatus = selectedStatus === "All Statuses" || employee.status === selectedStatus;
    
    return matchesSearch && matchesDepartment && matchesHealthPlan && matchesStatus;
  });
  
  // Handle employee selection (checkbox)
  const toggleEmployeeSelection = (employeeId: number) => {
    if (selectedEmployees.includes(employeeId)) {
      setSelectedEmployees(selectedEmployees.filter(id => id !== employeeId));
    } else {
      setSelectedEmployees([...selectedEmployees, employeeId]);
    }
  };
  
  // Handle bulk selection
  const toggleSelectAll = () => {
    if (selectedEmployees.length === filteredEmployees.length) {
      setSelectedEmployees([]);
    } else {
      setSelectedEmployees(filteredEmployees.map(e => e.id));
    }
  };

  // Validate form fields
  const validateForm = () => {
    const errors = {
      firstName: "",
      email: "",
      phoneNumber: "",
      department: "",
      role: "",
      healthPlan: ""
    };

    let isValid = true;

    // First Name validation
    if (!formData.firstName.trim()) {
      errors.firstName = "First Name is required";
      isValid = false;
    }

    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required";
      isValid = false;
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      errors.email = "Please enter a valid email address";
      isValid = false;
    }

    // Phone Number validation
    if (!formData.phoneNumber.trim()) {
      errors.phoneNumber = "Phone Number is required";
      isValid = false;
    } else if (!/^[\+]?[1-9][\d]{0,15}$/.test(formData.phoneNumber.replace(/[\s\-\(\)]/g, ''))) {
      errors.phoneNumber = "Please enter a valid phone number";
      isValid = false;
    }

    // Department validation
    if (!formData.department) {
      errors.department = "Department is required";
      isValid = false;
    }

    // Role validation
    if (!formData.role.trim()) {
      errors.role = "Role is required";
      isValid = false;
    }

    // Health Plan validation
    if (!formData.healthPlan) {
      errors.healthPlan = "Health Plan is required";
      isValid = false;
    }

    setFormErrors(errors);
    return isValid;
  };

  // Handle form submission
  const handleAddEmployee = async () => {
    if (validateForm()) {
      setIsCreatingEmployee(true);

      try {
        const employeeData: CreateEmployeeRequest = {
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email,
          phoneNumber: formData.phoneNumber || undefined,
          department: formData.department,
          role: formData.role,
          healthPlan: formData.healthPlan
        };

        console.log("Registering employee:", employeeData);
        const response = await employeeService.registerEmployee(employeeData);

        if (response.success) {
          console.log("Employee registered successfully:", response.data);

          // Reset form
          setFormData({
            firstName: "",
            lastName: "",
            email: "",
            phoneNumber: "",
            department: "",
            role: "",
            healthPlan: ""
          });

          // Clear errors
          setFormErrors({
            firstName: "",
            email: "",
            phoneNumber: "",
            department: "",
            role: "",
            healthPlan: ""
          });

          // Close dialog
          setIsAddEmployeeOpen(false);

          // Reload employees to show the new one
          loadEmployees();

          // Show success message with member_id information
          const memberInfo = response.data?.member_id ? ` (Member ID: ${response.data.member_id})` : '';
          alert(`Employee ${employeeData.firstName} ${employeeData.lastName} registered successfully in Cognito${memberInfo}!`);
        } else {
          // Handle API error
          console.error("Failed to register employee:", response.error);
          alert(`Failed to register employee: ${response.error?.message || 'Unknown error'}`);
        }
      } catch (error) {
        console.error("Error registering employee:", error);
        alert("An unexpected error occurred while registering the employee.");
      } finally {
        setIsCreatingEmployee(false);
      }
    }
  };

  // Handle form field changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear error when user starts typing
    if (formErrors[field as keyof typeof formErrors]) {
      setFormErrors(prev => ({ ...prev, [field]: "" }));
    }
  };
  
  return (
    <div className="container mx-auto px-4 py-4">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Employee Management</h1>
            <p className="text-gray-500 mt-1">Manage employees and their wellness benefits</p>
          </div>
          <div className="flex gap-4">
            <Dialog open={isAddEmployeeOpen} onOpenChange={setIsAddEmployeeOpen}>
              <DialogTrigger asChild>
                <Button>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Add Employee
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-[600px]">
                <DialogHeader>
                  <DialogTitle>Add New Employee</DialogTitle>
                  <DialogDescription>
                    Enter the details of the new employee below.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid grid-cols-2 gap-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First Name *</Label>
                    <Input
                      id="firstName"
                      placeholder="First Name"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange("firstName", e.target.value)}
                      className={formErrors.firstName ? "border-red-500" : ""}
                    />
                    {formErrors.firstName && (
                      <p className="text-sm text-red-500">{formErrors.firstName}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last Name</Label>
                    <Input
                      id="lastName"
                      placeholder="Last Name"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange("lastName", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email">Email *</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="Email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={formErrors.email ? "border-red-500" : ""}
                    />
                    {formErrors.email && (
                      <p className="text-sm text-red-500">{formErrors.email}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phoneNumber" className="flex items-center space-x-1">
                      <span>Phone Number *</span>
                    </Label>
                    <Input
                      id="phoneNumber"
                      type="tel"
                      placeholder="+****************"
                      value={formData.phoneNumber}
                      onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                      className={formErrors.phoneNumber ? "border-red-500" : ""}
                    />
                    {formErrors.phoneNumber && (
                      <p className="text-sm text-red-500">{formErrors.phoneNumber}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="department">Department *</Label>
                    <Select
                      value={formData.department}
                      onValueChange={(value) => handleInputChange("department", value)}
                    >
                      <SelectTrigger className={formErrors.department ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select department" />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingDepartments ? (
                          <SelectItem value="" disabled>Loading departments...</SelectItem>
                        ) : (
                          apiDepartments.length > 0 ? (
                            apiDepartments.map((dept) => (
                              <SelectItem key={dept.id} value={dept.name}>{dept.name}</SelectItem>
                            ))
                          ) : (
                            departments.filter(d => d !== "All Departments").map((dept) => (
                              <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                            ))
                          )
                        )}
                      </SelectContent>
                    </Select>
                    {formErrors.department && (
                      <p className="text-sm text-red-500">{formErrors.department}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="role">Role *</Label>
                    <Input
                      id="role"
                      placeholder="Role"
                      value={formData.role}
                      onChange={(e) => handleInputChange("role", e.target.value)}
                      className={formErrors.role ? "border-red-500" : ""}
                    />
                    {formErrors.role && (
                      <p className="text-sm text-red-500">{formErrors.role}</p>
                    )}
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="healthPlan">Health Plan *</Label>
                    <Select
                      value={formData.healthPlan}
                      onValueChange={(value) => handleInputChange("healthPlan", value)}
                    >
                      <SelectTrigger className={formErrors.healthPlan ? "border-red-500" : ""}>
                        <SelectValue placeholder="Select health plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {loadingHealthPlans ? (
                          <SelectItem value="" disabled>Loading health plans...</SelectItem>
                        ) : (
                          apiHealthPlans.length > 0 ? (
                            apiHealthPlans.map((plan) => (
                              <SelectItem key={plan.id} value={plan.name}>{plan.name}</SelectItem>
                            ))
                          ) : (
                            healthPlans.filter(p => p !== "All Plans").map((plan) => (
                              <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                            ))
                          )
                        )}
                      </SelectContent>
                    </Select>
                    {formErrors.healthPlan && (
                      <p className="text-sm text-red-500">{formErrors.healthPlan}</p>
                    )}
                  </div>

                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setIsAddEmployeeOpen(false)}
                    disabled={isCreatingEmployee}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    onClick={handleAddEmployee}
                    disabled={isCreatingEmployee}
                  >
                    {isCreatingEmployee ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      'Add Employee'
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>
        
        {/* Employee Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList>
            <TabsTrigger value="current">Current Employees</TabsTrigger>
            <TabsTrigger value="pending">Pending Enrollments</TabsTrigger>
          </TabsList>
          
          {/* Current Employees Tab */}
          <TabsContent value="current">
            <Card>
              <CardHeader className="p-4 pb-0">
                <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                  <div className="relative w-full md:w-auto md:min-w-[320px]">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-500" />
                    <Input 
                      placeholder="Search employees..." 
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <div className="flex flex-wrap gap-2 items-center">
                    <Select value={selectedDepartment} onValueChange={setSelectedDepartment}>
                      <SelectTrigger className="w-[180px]">
                        <SelectValue placeholder="Department" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((dept) => (
                          <SelectItem key={dept} value={dept}>{dept}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select value={selectedHealthPlan} onValueChange={setSelectedHealthPlan}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Health Plan" />
                      </SelectTrigger>
                      <SelectContent>
                        {healthPlans.map((plan) => (
                          <SelectItem key={plan} value={plan}>{plan}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    
                    <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                      <SelectTrigger className="w-[150px]">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="All Statuses">All Statuses</SelectItem>
                        <SelectItem value="Active">Active</SelectItem>
                        <SelectItem value="Inactive">Inactive</SelectItem>
                        <SelectItem value="On Leave">On Leave</SelectItem>
                      </SelectContent>
                    </Select>
                    
                    <Button variant="outline" size="icon">
                      <RefreshCw className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox 
                          checked={selectedEmployees.length > 0 && selectedEmployees.length === filteredEmployees.length} 
                          onCheckedChange={toggleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Name</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Health Plan</TableHead>
                      <TableHead>Wellness Score</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEmployees.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <Checkbox 
                            checked={selectedEmployees.includes(employee.id)} 
                            onCheckedChange={() => toggleEmployeeSelection(employee.id)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <p className="font-medium">{employee.firstName} {employee.lastName}</p>
                            <p className="text-sm text-gray-500">{employee.email}</p>
                          </div>
                        </TableCell>
                        <TableCell>{employee.department}</TableCell>
                        <TableCell>{employee.role}</TableCell>
                        <TableCell>
                          <Badge className={
                            employee.healthPlan === "Premium" ? "bg-indigo-100 text-indigo-800 hover:bg-indigo-100" :
                            employee.healthPlan === "Standard" ? "bg-blue-100 text-blue-800 hover:bg-blue-100" :
                            "bg-gray-100 text-gray-800 hover:bg-gray-100"
                          }>
                            {employee.healthPlan}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <div className="h-2 w-16 bg-gray-200 rounded-full overflow-hidden">
                              <div 
                                className={`h-full ${
                                  employee.wellnessScore >= 80 ? "bg-green-500" :
                                  employee.wellnessScore >= 60 ? "bg-amber-500" :
                                  "bg-red-500"
                                }`}
                                style={{ width: `${employee.wellnessScore}%` }}
                              />
                            </div>
                            <span className="text-sm">{employee.wellnessScore}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={
                            employee.status === "Active" ? "bg-green-100 text-green-800 hover:bg-green-100" :
                            employee.status === "Inactive" ? "bg-red-100 text-red-800 hover:bg-red-100" :
                            "bg-amber-100 text-amber-800 hover:bg-amber-100"
                          }>
                            {employee.status}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="ghost" size="icon">
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Mail className="h-4 w-4" />
                            </Button>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
              <CardFooter className="flex justify-between p-4 border-t">
                <div className="text-sm text-gray-500">
                  Showing {filteredEmployees.length} of {employees.length} employees
                </div>
                <div className="flex gap-2">
                  {selectedEmployees.length > 0 ? (
                    <>
                      <Button variant="outline" size="sm">
                        Bulk Edit
                      </Button>
                      <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                        <Trash2 className="mr-2 h-4 w-4" />
                        Remove
                      </Button>
                    </>
                  ) : (
                    <>
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Export
                      </Button>
                      <Button variant="outline" size="sm">
                        <Upload className="mr-2 h-4 w-4" />
                        Import
                      </Button>
                    </>
                  )}
                </div>
              </CardFooter>
            </Card>
          </TabsContent>
          
          {/* Pending Enrollments Tab */}
          <TabsContent value="pending">
            <Card>
              <CardHeader>
                <CardTitle>Pending Enrollments</CardTitle>
                <CardDescription>Employees who have been invited but haven't completed enrollment</CardDescription>
              </CardHeader>
              <CardContent className="p-0 overflow-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Name</TableHead>
                      <TableHead>Email</TableHead>
                      <TableHead>Department</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Invite Sent</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {pendingEnrollments.map((employee) => (
                      <TableRow key={employee.id}>
                        <TableCell>
                          <div>
                            <p className="font-medium">{employee.firstName} {employee.lastName}</p>
                          </div>
                        </TableCell>
                        <TableCell>{employee.email}</TableCell>
                        <TableCell>{employee.department}</TableCell>
                        <TableCell>{employee.role}</TableCell>
                        <TableCell>{new Date(employee.inviteSent).toLocaleDateString()}</TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-2">
                            <Button variant="outline" size="sm">
                              <Mail className="mr-2 h-4 w-4" />
                              Resend Invite
                            </Button>
                            <Button variant="ghost" size="icon">
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
  );
};

export default EmployeeManagement;