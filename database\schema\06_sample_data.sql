-- =====================================================
-- SAMPLE DATA FOR APERION HEALTH DATABASE
-- =====================================================

-- Insert sample system administrators
INSERT INTO command_center.system_administrators (
  cognito_user_id, first_name, last_name, email, role, permissions, department
) VALUES 
  ('admin-001', 'John', 'Admin', '<EMAIL>', 'super_admin', '["admin:system", "admin:users", "admin:analytics"]', 'IT'),
  ('admin-002', 'Sarah', 'Manager', '<EMAIL>', 'system_admin', '["admin:users", "admin:analytics"]', 'Operations');

-- Insert sample user references
INSERT INTO shared_data.user_references (
  cognito_user_id, user_type, service_user_id, email, first_name, last_name
) VALUES 
  ('demo-member-001', 'member', gen_random_uuid(), '<EMAIL>', '<PERSON>', 'Doe'),
  ('demo-employer-001', 'employer', gen_random_uuid(), '<EMAIL>', 'Jane', 'Smith'),
  ('demo-coach-001', 'wellness_coach', gen_random_uuid(), '<EMAIL>', 'Dr. Sarah', 'Wilson'),
  ('demo-admin-001', 'system_admin', gen_random_uuid(), '<EMAIL>', 'Admin', 'User'),
  ('demo-lms-001', 'lms_creator', gen_random_uuid(), '<EMAIL>', 'Prof. Mike', 'Johnson');

-- Insert sample employer
INSERT INTO employer_service.employers (
  id, cognito_user_id, company_name, company_code, industry, company_size, 
  contact_email, subscription_plan, max_members
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-employer-001'),
  'demo-employer-001', 
  'TechCorp Solutions', 
  'TECHCORP001', 
  'Technology', 
  '51-200',
  '<EMAIL>', 
  'premium', 
  200
);

-- Insert sample member
INSERT INTO member_service.members (
  id, cognito_user_id, employer_id, first_name, last_name, email, 
  date_of_birth, gender, onboarding_completed
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
  'demo-member-001',
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-employer-001'),
  'John', 
  'Doe', 
  '<EMAIL>',
  '1990-05-15',
  'male',
  true
);

-- Insert sample wellness coach
INSERT INTO wellness_service.wellness_coaches (
  id, cognito_user_id, first_name, last_name, email, 
  specializations, experience_years, max_members, onboarding_completed
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-coach-001'),
  'demo-coach-001',
  'Dr. Sarah', 
  'Wilson', 
  '<EMAIL>',
  ARRAY['nutrition', 'weight_management', 'stress_reduction'],
  8,
  50,
  true
);

-- Insert sample learning modules
INSERT INTO lms_service.learning_modules (
  title, description, category, difficulty_level, estimated_duration_minutes,
  learning_objectives, content_type, status, created_by, published_at, featured
) VALUES 
  (
    'Introduction to Wellness',
    'A comprehensive introduction to personal wellness and health management.',
    'Health & Wellness',
    'beginner',
    45,
    ARRAY['Understand basic wellness principles', 'Learn about nutrition basics', 'Identify stress factors'],
    'video',
    'published',
    (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-lms-001'),
    NOW(),
    true
  ),
  (
    'Stress Management Techniques',
    'Learn effective techniques for managing workplace and personal stress.',
    'Mental Health',
    'intermediate',
    60,
    ARRAY['Identify stress triggers', 'Practice relaxation techniques', 'Develop coping strategies'],
    'interactive',
    'published',
    (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-lms-001'),
    NOW(),
    false
  );

-- Insert sample health metrics
INSERT INTO member_service.member_health_metrics (
  member_id, metric_type, value, unit, source, verified
) VALUES 
  (
    (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
    'weight',
    175.5,
    'lbs',
    'manual',
    true
  ),
  (
    (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
    'blood_pressure_systolic',
    120,
    'mmHg',
    'device',
    true
  );

-- Insert sample health goals
INSERT INTO member_service.member_health_goals (
  member_id, goal_type, title, description, target_value, target_unit, target_date
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
  'weight_loss',
  'Lose 10 pounds',
  'Achieve a healthier weight through diet and exercise',
  165.0,
  'lbs',
  '2024-12-31'
);

-- Insert sample coach assignment
INSERT INTO wellness_service.member_coach_assignments (
  member_id, coach_id, assignment_reason, assignment_type
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-coach-001'),
  'Initial wellness consultation and ongoing support',
  'general'
);

-- Insert sample learning enrollment
INSERT INTO lms_service.learner_enrollments (
  learner_id, learner_type, module_id, status, progress_percentage
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-member-001'),
  'member',
  (SELECT id FROM lms_service.learning_modules WHERE title = 'Introduction to Wellness'),
  'in_progress',
  65.0
);

-- Insert sample system settings
INSERT INTO command_center.system_settings (
  setting_key, setting_value, setting_type, category, description, is_public
) VALUES 
  ('platform_name', '"Aperion Health"', 'string', 'branding', 'Platform display name', true),
  ('max_file_upload_size', '10485760', 'number', 'system', 'Maximum file upload size in bytes', false),
  ('maintenance_mode', 'false', 'boolean', 'system', 'Enable maintenance mode', false),
  ('supported_languages', '["en", "es", "fr"]', 'array', 'localization', 'Supported platform languages', true);

-- Insert sample platform usage stats
INSERT INTO command_center.platform_usage_stats (
  date, total_users, active_users, new_registrations, total_employers, 
  total_members, total_coaches, system_uptime_percentage
) VALUES (
  CURRENT_DATE,
  150,
  89,
  5,
  12,
  120,
  8,
  99.9
);

-- Insert sample wellness program
INSERT INTO wellness_service.wellness_programs (
  program_name, description, program_type, duration_weeks, max_participants,
  coach_id, start_date, end_date, created_by
) VALUES (
  'Corporate Wellness Challenge',
  'A 12-week comprehensive wellness program focusing on nutrition, exercise, and stress management.',
  'weight_management',
  12,
  25,
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-coach-001'),
  CURRENT_DATE + INTERVAL '1 week',
  CURRENT_DATE + INTERVAL '13 weeks',
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-coach-001')
);

-- Insert sample employee invitation
INSERT INTO employer_service.employee_invitations (
  employer_id, email, first_name, last_name, department, job_title,
  activation_code, created_by
) VALUES (
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-employer-001'),
  '<EMAIL>',
  'Alice',
  'Johnson',
  'Engineering',
  'Software Developer',
  'INVITE-' || UPPER(SUBSTRING(gen_random_uuid()::text, 1, 8)),
  (SELECT service_user_id FROM shared_data.user_references WHERE cognito_user_id = 'demo-employer-001')
);
