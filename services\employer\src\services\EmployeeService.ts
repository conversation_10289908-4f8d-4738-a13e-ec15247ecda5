import { logger } from '../utils/logger';
import { ApiResponse } from '@aperion/shared';
import {
  Employee,
  CreateEmployeeRequest,
  CreateEmployeeResponse,
  EmployeeQueryParams,
  Department,
  HealthPlan
} from '../types/employee';
import { EmployeeRepository } from '../repositories/EmployeeRepository';
import { signUpUser } from './CognitoService';
import { EmployeeModel } from '../models/EmployeeModel';
import { EmailService } from './EmailService';

// Import Cognito service - we'll need to copy the necessary functions
// since we can't directly import from command-center service
import {
  CognitoIdentityProviderClient,
  AdminCreateUserCommand,
  AdminSetUserPasswordCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import * as dotenv from 'dotenv';

dotenv.config();

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Interface for employee creation in Cognito
interface EmployeeCognitoParams {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
}

interface CognitoEmployeeResponse {
  userSub: string;
  temporaryPassword: string;
  isTemporary: boolean;
}

/**
 * Generate a secure random password
 */
const generateSecurePassword = (): string => {
  const length = 12;
  const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%^&*';
  let password = '';

  // Ensure at least one character from each required type
  password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // Uppercase
  password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // Lowercase
  password += '**********'[Math.floor(Math.random() * 10)]; // Number
  password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // Special char

  // Fill the rest randomly
  for (let i = 4; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // Shuffle the password
  return password.split('').sort(() => Math.random() - 0.5).join('');
};

/**
 * Create employee user in AWS Cognito
 */
const createEmployeeInCognito = async (userData: EmployeeCognitoParams): Promise<CognitoEmployeeResponse> => {
  const { firstName, lastName, email, phone } = userData;

  // Determine username (prefer phone number, fallback to email)
  const username = phone || email;

  try {
    logger.info('Creating employee in Cognito:', { email, username });

    // Generate a temporary password
    const temporaryPassword = generateSecurePassword();

    // Prepare user attributes
    const userAttributes = [
      { Name: 'email', Value: email },
      { Name: 'email_verified', Value: 'true' },
      { Name: 'given_name', Value: firstName },
      { Name: 'family_name', Value: lastName },
      { Name: 'custom:roleId', Value: '3' }, // Employee role ID
    ];

    // Add phone number if provided
    if (phone) {
      userAttributes.push({ Name: 'phone_number', Value: phone });
    }

    // Create user command
    const createUserCommand = new AdminCreateUserCommand({
      UserPoolId: process.env.USERPOOLID!,
      Username: username,
      UserAttributes: userAttributes,
      TemporaryPassword: temporaryPassword,
      MessageAction: 'SUPPRESS', // Suppress welcome email for admin-created users
    });

    const createResponse = await cognitoClient.send(createUserCommand);

    if (!createResponse.User?.Username) {
      throw new Error('Failed to create user - no username returned from Cognito');
    }

    // Extract the actual Cognito User Sub ID from user attributes
    const responseAttributes = createResponse.User.Attributes || [];
    const subAttribute = responseAttributes.find(attr => attr.Name === 'sub');
    const cognitoUserSub = subAttribute?.Value;

    if (!cognitoUserSub) {
      throw new Error('Failed to get Cognito User Sub ID from created user');
    }

    // Set the password as permanent (optional - user can change it later)
    const setPasswordCommand = new AdminSetUserPasswordCommand({
      UserPoolId: process.env.USERPOOLID!,
      Username: username,
      Password: temporaryPassword,
      Permanent: false, // Keep as temporary so user must change on first login
    });

    await cognitoClient.send(setPasswordCommand);

    logger.info('Successfully created employee in Cognito:', {
      email,
      username,
      cognitoUserSub
    });

    return {
      userSub: cognitoUserSub,
      temporaryPassword,
      isTemporary: true,
    };

  } catch (error: any) {
    logger.error('Error creating employee in Cognito:', {
      email,
      username,
      error: error.message,
      code: error.name
    });

    // Handle specific Cognito errors
    if (error.name === 'UsernameExistsException') {
      throw new Error(`User with email ${email} already exists in Cognito`);
    } else if (error.name === 'InvalidPasswordException') {
      throw new Error('Generated password does not meet Cognito password policy');
    } else {
      throw new Error(`Failed to create user in Cognito: ${error.message}`);
    }
  }
};

export class EmployeeService {
  private employeeRepository: EmployeeRepository;
  private employeeModel: EmployeeModel;
  private emailService: EmailService;

  constructor() {
    this.employeeRepository = new EmployeeRepository();
    this.employeeModel = new EmployeeModel();
    this.emailService = new EmailService();
  }

  /**
   * Create a new employee with Cognito integration
   */
  async createEmployee(
    employerId: string, 
    employeeData: CreateEmployeeRequest
  ): Promise<ApiResponse<CreateEmployeeResponse>> {
    try {
      logger.info('Creating employee:', { employerId, email: employeeData.email });

      // Step 1: Validate input data
      const { firstName, lastName, email, phoneNumber, department, role } = employeeData;

      // Step 2: Check if employee already exists
      const existingEmployee = await this.employeeRepository.findByEmail(employerId, email);
      if (existingEmployee) {
        return {
          success: false,
          error: {
            code: 'EMPLOYEE_EXISTS',
            message: 'Employee with this email already exists',
            timestamp: new Date().toISOString()
          }
        };
      }

      // Step 3: Get or create department
      let departmentId: string | undefined;
      try {
        const dept = await this.employeeRepository.findOrCreateDepartment(employerId, department);
        departmentId = dept.id;
      } catch (error) {
        logger.warn('Failed to create/find department, proceeding without department:', error);
      }

      // Step 4: Create user in Cognito
      const cognitoParams: EmployeeCognitoParams = {
        firstName,
        lastName,
        email,
        phone: phoneNumber,
      };

      const cognitoResponse = await createEmployeeInCognito(cognitoParams);
      
      // Step 5: Create employee record in database
      const employeeRecord: Omit<Employee, 'id' | 'createdAt' | 'updatedAt'> = {
        employerId,
        memberId: cognitoResponse.userSub, // Use Cognito UserSub as member_id
        employeeId: undefined, // Can be set later by employer
        firstName,
        lastName,
        email,
        phone: phoneNumber,
        departmentId,
        jobTitle: role,
        hireDate: new Date(),
        employmentStatus: 'active',
        managerId: undefined,
        salaryBand: undefined,
        benefitsEligible: true,
        wellnessProgramEnrolled: false,
        lastHealthScreening: undefined
      };

      const createdEmployee = await this.employeeRepository.create(employeeRecord);

      logger.info('Successfully created employee:', {
        employeeId: createdEmployee.id,
        cognitoUserSub: cognitoResponse.userSub,
        email
      });

      const response: CreateEmployeeResponse = {
        employee: createdEmployee,
        cognitoUserSub: cognitoResponse.userSub,
        temporaryPassword: cognitoResponse.temporaryPassword,
        isTemporary: cognitoResponse.isTemporary
      };

      return {
        success: true,
        data: response
      };

    } catch (error: any) {
      logger.error('Error creating employee:', {
        employerId,
        email: employeeData.email,
        error: error.message,
        stack: error.stack
      });

      // Handle specific Cognito errors
      if (error.message?.includes('already exists in Cognito')) {
        return {
          success: false,
          error: {
            code: 'EMAIL_EXISTS',
            message: 'An account with this email already exists',
            timestamp: new Date().toISOString()
          }
        };
      }

      return {
        success: false,
        error: {
          code: 'EMPLOYEE_CREATION_FAILED',
          message: error.message || 'Failed to create employee',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Create a new employee following Command Center pattern
   * 1. Check database for existing user
   * 2. Create in Cognito with role ID 3
   * 3. Insert into shared_data.user_references
   * 4. Handle rollback if needed
   */
  async createEmployeeCommandCenterPattern(
    employeeData: CreateEmployeeRequest
  ): Promise<ApiResponse<{ id?: string; member_id: string; email: string; phone?: string; role: string; status: string; message: string }>> {
    try {
      logger.info('Creating new employee:', {
        email: employeeData.email,
        phone: employeeData.phoneNumber,
      });

      // Step 1: Check if user with email already exists (Command Center pattern)
      const existingUsers = await this.employeeModel.getEmployeesByEmail(employeeData.email);

      if (existingUsers.length > 0) {
        return {
          success: false,
          error: {
            code: 'EMPLOYEE_ALREADY_EXISTS',
            message: 'Employee with this email already exists in our system',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Step 2: Validate required phone number
      if (!employeeData.phoneNumber) {
        return {
          success: false,
          error: {
            code: 'MISSING_PHONE',
            message: 'Phone number is required for employee registration',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Step 3: Create in Cognito with role ID 3 (following Command Center pattern)
      let cognitoResponse: { userSub: string; password: string };
      try {
        cognitoResponse = await signUpUser({
          phoneNumber: employeeData.phoneNumber,
          email: employeeData.email,
          roleId: 3, // Employee role ID
        });

        logger.info('Successfully registered employee in Cognito:', {
          email: employeeData.email,
          userSub: cognitoResponse.userSub,
        });
      } catch (cognitoError: any) {
        logger.error('Failed to register employee in Cognito:', {
          email: employeeData.email,
          error: cognitoError.message,
        });

        return {
          success: false,
          error: {
            code: 'COGNITO_CREATION_ERROR',
            message: cognitoError.message || 'Failed to register employee in Cognito',
            timestamp: new Date().toISOString(),
          },
        };
      }

      // Step 4: Create in database (shared_data.user_references)
      try {
        const employeeDataWithCognito = {
          ...employeeData,
          cognitoUserId: cognitoResponse.userSub,
          userType: 'member', // Critical: must be 'member' for employees
        };

        const newEmployee = await this.employeeModel.createEmployee(employeeDataWithCognito);

        logger.info('Successfully created complete employee:', {
          email: newEmployee.email,
          id: newEmployee.id,
          cognitoUserId: newEmployee.cognitoUserId,
        });

        // Step 5: Send account confirmation email invitation
        try {
          const emailResult = await this.emailService.sendEmployeeInvitation({
            email: employeeData.email,
            firstName: employeeData.firstName,
            lastName: employeeData.lastName,
            cognitoUserSub: cognitoResponse.userSub,
          });

          if (emailResult.success) {
            logger.info('Employee invitation email sent successfully:', {
              email: employeeData.email,
              firstName: employeeData.firstName,
              lastName: employeeData.lastName,
            });
          } else {
            logger.warn('Failed to send employee invitation email (non-blocking):', {
              email: employeeData.email,
              error: emailResult.error,
              message: emailResult.message,
            });
          }
        } catch (emailError: any) {
          // Email sending failure should not block employee creation
          logger.error('Error sending employee invitation email (non-blocking):', {
            email: employeeData.email,
            error: emailError.message,
          });
        }

        return {
          success: true,
          data: {
            id: newEmployee.id,
            member_id: cognitoResponse.userSub,
            email: newEmployee.email,
            phone: newEmployee.phone,
            role: 'member',
            status: 'pending',  // Status is 'pending' until employee completes registration
            message: 'Employee registered successfully and invitation email sent',
          },
        };
      } catch (dbError: any) {
        // Step 5: Rollback Cognito user if database fails (Command Center pattern)
        logger.error('Database creation failed after Cognito success - initiating rollback:', {
          email: employeeData.email,
          cognitoUserSub: cognitoResponse.userSub,
          error: dbError.message,
        });

        // TODO: Implement Cognito cleanup
        // await deleteUserFromCognito(employeeData.phoneNumber);

        return {
          success: false,
          error: {
            code: 'DATABASE_CREATION_ERROR',
            message: 'Failed to create employee record after Cognito registration',
            timestamp: new Date().toISOString(),
          },
        };
      }
    } catch (error) {
      logger.error('Error in EmployeeService.createEmployeeCommandCenterPattern:', error);
      return {
        success: false,
        error: {
          code: 'INTERNAL_SERVER_ERROR',
          message: 'An unexpected error occurred during employee creation',
          timestamp: new Date().toISOString(),
        },
      };
    }
  }

  /**
   * Get employees for an employer with pagination and filtering
   */
  async getEmployees(
    employerId: string, 
    params: EmployeeQueryParams = {}
  ): Promise<ApiResponse<Employee[]>> {
    try {
      logger.info('Fetching employees:', { employerId, params });

      const { employees, total } = await this.employeeRepository.findByEmployer(employerId, params);
      
      const page = params.page || 1;
      const limit = params.limit || 20;
      const totalPages = Math.ceil(total / limit);

      return {
        success: true,
        data: employees,
        meta: {
          page,
          limit,
          total,
          totalPages,
          hasNext: page < totalPages,
          hasPrev: page > 1
        }
      };

    } catch (error: any) {
      logger.error('Error fetching employees:', {
        employerId,
        error: error.message
      });

      return {
        success: false,
        error: {
          code: 'FETCH_EMPLOYEES_FAILED',
          message: error.message || 'Failed to fetch employees',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get departments for an employer
   */
  async getDepartments(employerId: string): Promise<ApiResponse<Department[]>> {
    try {
      const departments = await this.employeeRepository.getDepartments(employerId);
      
      return {
        success: true,
        data: departments
      };

    } catch (error: any) {
      logger.error('Error fetching departments:', {
        employerId,
        error: error.message
      });

      return {
        success: false,
        error: {
          code: 'FETCH_DEPARTMENTS_FAILED',
          message: error.message || 'Failed to fetch departments',
          timestamp: new Date().toISOString()
        }
      };
    }
  }

  /**
   * Get available health plans
   */
  async getHealthPlans(): Promise<ApiResponse<HealthPlan[]>> {
    try {
      // For now, return static health plans
      // In the future, this could be dynamic from database
      const healthPlans: HealthPlan[] = [
        {
          id: '1',
          name: 'Basic',
          description: 'Basic health coverage',
          type: 'basic',
          monthlyPremium: 150,
          deductible: 2000,
          status: 'active'
        },
        {
          id: '2',
          name: 'Standard',
          description: 'Standard health coverage with additional benefits',
          type: 'standard',
          monthlyPremium: 250,
          deductible: 1000,
          status: 'active'
        },
        {
          id: '3',
          name: 'Premium',
          description: 'Premium health coverage with comprehensive benefits',
          type: 'premium',
          monthlyPremium: 400,
          deductible: 500,
          status: 'active'
        }
      ];

      return {
        success: true,
        data: healthPlans
      };

    } catch (error: any) {
      logger.error('Error fetching health plans:', error);

      return {
        success: false,
        error: {
          code: 'FETCH_HEALTH_PLANS_FAILED',
          message: error.message || 'Failed to fetch health plans',
          timestamp: new Date().toISOString()
        }
      };
    }
  }
}
