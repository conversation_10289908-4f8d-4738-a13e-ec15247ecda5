import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Loader2, User, Mail, Phone, Building2, CreditCard } from 'lucide-react';

// Define UserRole enum locally to avoid import issues
enum UserRole {
  MEMBER = 'member',
  EMPLOYER = 'employer',
  WELLNESS_COACH = 'wellness-coach',
  SYSTEM_ADMIN = 'system-admin',
}

// Simple form data interface
interface CreateUserFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
  role: UserRole;
  company?: string | undefined;
  subscription?: string | undefined;
  userType: string;
}

interface CreateUserModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateUser: (userData: CreateUserFormData) => Promise<boolean>;
  companies: string[];
}

// Role configurations
const roleConfigs = {
  [UserRole.MEMBER]: {
    label: 'Member',
    description: 'Individual employee with health benefits access',
    color: 'bg-purple-100 text-purple-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone', 'company', 'subscription'],
    defaultCompany: 'TechCorp Solutions',
    defaultSubscription: 'Premium Health Plan',
  },
  [UserRole.EMPLOYER]: {
    label: 'Employer',
    description: 'Company administrator with employee management access',
    color: 'bg-blue-100 text-blue-800',
    requiredFields: ['firstName', 'email'], // firstName will store company name for employers
    optionalFields: ['phone', 'subscription'],
    defaultCompany: 'Global Health Corp',
    defaultSubscription: 'Enterprise',
  },
  [UserRole.WELLNESS_COACH]: {
    label: 'Wellness Coach',
    description: 'Health coaching professional with member access',
    color: 'bg-green-100 text-green-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone', 'company'],
    defaultCompany: 'WellnessTech Inc',
    defaultSubscription: undefined,
  },
  [UserRole.SYSTEM_ADMIN]: {
    label: 'System Admin',
    description: 'Platform administrator with full system access',
    color: 'bg-red-100 text-red-800',
    requiredFields: ['firstName', 'lastName', 'email'],
    optionalFields: ['phone'],
    defaultCompany: 'Platform Administration',
    defaultSubscription: undefined,
  },
};

export function CreateUserModal({ open, onOpenChange, onCreateUser, companies }: CreateUserModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: UserRole.MEMBER,
    company: '',
    subscription: '',
  });

  console.log('CreateUserModal render - open:', open);

  const roleConfig = roleConfigs[formData.role];

  // Update default values when role changes
  React.useEffect(() => {
    if (roleConfig) {
      setFormData(prev => ({
        ...prev,
        company: roleConfig.defaultCompany || '',
        subscription: roleConfig.defaultSubscription || '',
      }));
    }
  }, [formData.role, roleConfig]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Map role to userType for backend
      const userTypeMapping = {
        [UserRole.MEMBER]: 'member',
        [UserRole.EMPLOYER]: 'employer',
        [UserRole.WELLNESS_COACH]: 'wellness_coach',
        [UserRole.SYSTEM_ADMIN]: 'system_admin',
      };

      const userData = {
        ...formData,
        userType: userTypeMapping[formData.role],
        phone: formData.phone || undefined,
        company: formData.company || undefined,
        subscription: formData.subscription || undefined,
        // For employers, set lastName to null since firstName contains company name
        lastName: formData.role === UserRole.EMPLOYER ? "null" : formData.lastName,
      };

      const success = await onCreateUser(userData);
      if (success) {
        // Reset form
        setFormData({
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          role: UserRole.MEMBER,
          company: '',
          subscription: '',
        });
        onOpenChange(false);
      }
    } catch (error) {
      console.error('Error creating user:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    // Reset form
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      role: UserRole.MEMBER,
      company: '',
      subscription: '',
    });
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <User className="w-5 h-5" />
            <span>Create New User</span>
          </DialogTitle>
          <DialogDescription>
            Add a new user to the platform. Select a role to customize the form fields.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Role Selection */}
          <div className="space-y-2">
            <Label htmlFor="role">User Role *</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => setFormData(prev => ({ ...prev, role: value as UserRole }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a role" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(roleConfigs).map(([role, config]) => (
                  <SelectItem key={role} value={role}>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${config.color}`}>
                        {config.label}
                      </Badge>
                      <span className="text-sm text-slate-600">{config.description}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Role Description */}
          {roleConfig && (
            <div className="p-3 bg-slate-50 dark:bg-slate-800 rounded-md">
              <div className="flex items-center space-x-2">
                <Badge className={`text-xs ${roleConfig.color}`}>
                  {roleConfig.label}
                </Badge>
                <span className="text-sm text-slate-600 dark:text-slate-400">
                  {roleConfig.description}
                </span>
              </div>
            </div>
          )}

          {/* Personal Information - Conditional based on role */}
          {formData.role === UserRole.EMPLOYER ? (
            // For employers, show only Company Name field
            <div className="space-y-2">
              <Label htmlFor="firstName">Company Name *</Label>
              <Input
                id="firstName"
                placeholder="Acme Corporation"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                required
              />
            </div>
          ) : (
            // For other roles, show First Name and Last Name
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  placeholder="John"
                  value={formData.firstName}
                  onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name *</Label>
                <Input
                  id="lastName"
                  placeholder="Doe"
                  value={formData.lastName}
                  onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                  required
                />
              </div>
            </div>
          )}

          {/* Contact Information */}
          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center space-x-1">
              <Mail className="w-4 h-4" />
              <span>Email Address *</span>
            </Label>
            <Input
              id="email"
              type="email"
              placeholder="<EMAIL>"
              value={formData.email}
              onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center space-x-1">
              <Phone className="w-4 h-4" />
              <span>Phone Number</span>
            </Label>
            <Input
              id="phone"
              placeholder="+****************"
              value={formData.phone}
              onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            />
          </div>

          {/* Company Information */}
          {roleConfig?.optionalFields.includes('company') && (
            <div className="space-y-2">
              <Label htmlFor="company" className="flex items-center space-x-1">
                <Building2 className="w-4 h-4" />
                <span>Company {roleConfig.requiredFields.includes('company') ? '*' : ''}</span>
              </Label>
              <Select
                value={formData.company}
                onValueChange={(value) => setFormData(prev => ({ ...prev, company: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select company" />
                </SelectTrigger>
                <SelectContent>
                  {companies.map((company) => (
                    <SelectItem key={company} value={company}>
                      {company}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {/* Subscription Information */}
          {roleConfig?.optionalFields.includes('subscription') && (
            <div className="space-y-2">
              <Label htmlFor="subscription" className="flex items-center space-x-1">
                <CreditCard className="w-4 h-4" />
                <span>Subscription Plan</span>
              </Label>
              <Select
                value={formData.subscription}
                onValueChange={(value) => setFormData(prev => ({ ...prev, subscription: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select subscription" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Basic Health Plan">Basic Health Plan</SelectItem>
                  <SelectItem value="Premium Health Plan">Premium Health Plan</SelectItem>
                  <SelectItem value="Enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleCancel}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              Create User
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
