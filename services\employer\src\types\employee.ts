import { z } from 'zod';

// Employee interfaces
export interface Employee {
  id: string;
  employerId: string;
  memberId: string; // Cognito UserSub
  employeeId?: string | undefined;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
  departmentId?: string | undefined;
  jobTitle?: string | undefined;
  hireDate?: Date | undefined;
  employmentStatus: 'active' | 'inactive' | 'terminated' | 'on_leave';
  managerId?: string | undefined;
  salaryBand?: string | undefined;
  benefitsEligible: boolean;
  wellnessProgramEnrolled: boolean;
  lastHealthScreening?: Date | undefined;
  createdAt: Date;
  updatedAt: Date;
}

// Employee creation request interface
export interface CreateEmployeeRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string | undefined;
  department: string; // Department name for now, will be converted to departmentId
  role: string; // Job title
  healthPlan: string; // Health plan name
}

// Employee creation response interface
export interface CreateEmployeeResponse {
  employee: Employee;
  cognitoUserSub: string;
  temporaryPassword: string;
  isTemporary: boolean;
}

// Department interface
export interface Department {
  id: string;
  employerId: string;
  name: string;
  description?: string;
  managerEmail?: string;
  budget?: number;
  memberCount: number;
  status: 'active' | 'inactive';
  createdAt: Date;
  updatedAt: Date;
}

// Health Plan interface (simplified for now)
export interface HealthPlan {
  id: string;
  name: string;
  description?: string;
  type: 'basic' | 'standard' | 'premium';
  monthlyPremium?: number;
  deductible?: number;
  status: 'active' | 'inactive';
}

// Validation schemas
export const createEmployeeSchema = z.object({
  firstName: z.string().min(1, 'First name is required').max(100),
  lastName: z.string().min(1, 'Last name is required').max(100),
  email: z.string().email('Invalid email format'),
  phoneNumber: z.string().optional(),
  department: z.string().min(1, 'Department is required'),
  role: z.string().min(1, 'Role is required'),
  healthPlan: z.string().min(1, 'Health plan is required'),
});

export type CreateEmployeeInput = z.infer<typeof createEmployeeSchema>;

// Employee query parameters
export interface EmployeeQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  department?: string;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

// Employee statistics
export interface EmployeeStatistics {
  totalEmployees: number;
  activeEmployees: number;
  inactiveEmployees: number;
  newEmployeesThisMonth: number;
  employeesByDepartment: Record<string, number>;
  employeesByStatus: Record<string, number>;
}
