import { motion } from 'framer-motion';
import { useState, useEffect, useCallback } from 'react';
import {
  Users,
  Search,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  Ban,
  CheckCircle,
  Clock,
  Mail,
  ChevronLeft,
  ChevronRight,
  Building2,
  RefreshCw,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useCommandCenterToastContext } from '@/components/command-center/CommandCenterToast';
import { useUsers } from '@/hooks/useUsers';
import { User } from '@/services/userService';
import { CreateUserModal } from '@/components/admin/CreateUserModal';
import { EditUserModal } from '@/components/admin/EditUserModal';
import { ConfirmationDialog, useConfirmationDialog, ConfirmationAction } from '@/components/admin/ConfirmationDialog';

// User interface is imported from services/userService

// Utility function to get display name based on user role
const getUserDisplayName = (user: User): string => {
  if (user.role === 'employer') {
    // For employers, firstName contains the company name
    return user.firstName;
  } else {
    // For other roles, use firstName + lastName
    return `${user.firstName} ${user.lastName}`;
  }
};

// Utility function to get user initials for avatar
const getUserInitials = (user: User): string => {
  if (user.role === 'employer') {
    // For employers, use first two letters of company name
    return user.firstName.substring(0, 2).toUpperCase();
  } else {
    // For other roles, use first letter of first and last name
    return `${user.firstName[0]}${user.lastName[0]}`;
  }
};

export function UserManagement() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedTab, setSelectedTab] = useState('all');
  const [companyFilter, setCompanyFilter] = useState('all');
  const [createUserModalOpen, setCreateUserModalOpen] = useState(false);
  const [editUserModalOpen, setEditUserModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const commandCenterToast = useCommandCenterToastContext();
  const { dialogState, showConfirmation, hideConfirmation } = useConfirmationDialog();

  // Debug logging for modal state
  console.log('UserManagement render - createUserModalOpen:', createUserModalOpen);

  // Use the real API hook
  const {
    users,
    loading,
    error,
    total,
    totalPages,
    currentPage,
    hasNext,
    hasPrev,
    statistics,
    companies,
    filterInfo, // Add filter metadata
    fetchUsers,
    setPage,
    createUser,
    updateUser,
    deleteUser,
    suspendUser,
    activateUser,
    updateQueryParams
  } = useUsers({ page: 1, limit: 10 }) as any;

  // Debounced search function
  const debouncedUpdateFilters = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout;
      return (searchTerm: string, selectedTab: string, companyFilter: string) => {
        clearTimeout(timeoutId);
        timeoutId = setTimeout(() => {
          const params: any = {};

          // Add search term
          if (searchTerm.trim()) {
            params.search = searchTerm.trim();
          }

          // Add role filter (tab selection)
          if (selectedTab !== 'all') {
            // Map tab values to UserRole enum values (with hyphens)
            const roleMapping: { [key: string]: string } = {
              'member': 'member',
              'employer': 'employer',
              'coach': 'wellness-coach',
              'admin': 'system-admin'
            };
            params.role = roleMapping[selectedTab] || selectedTab;
          } else {
            // Explicitly remove role filter for "All Users" tab
            params.role = undefined;
          }

          // Add company filter
          if (companyFilter !== 'all') {
            params.company = companyFilter;
          }

          console.log('Applying filters:', params);
          updateQueryParams(params);
        }, 100); // Reduced debounce for faster response
      };
    })(),
    [updateQueryParams]
  );

  // Immediate role filter function (no debounce for role changes)
  const applyImmediateRoleFilter = useCallback((role: string) => {
    console.log('Applying immediate role filter:', role);
    const params: any = {};

    // Add current search term if exists
    if (searchTerm.trim()) {
      params.search = searchTerm.trim();
    }

    // Add role filter with mapping - use hyphenated values to match UserRole enum
    if (role !== 'all') {
      const roleMapping: { [key: string]: string } = {
        'member': 'member',
        'employer': 'employer',
        'coach': 'wellness-coach',
        'admin': 'system-admin'
      };
      params.role = roleMapping[role] || role;
    } else {
      // Explicitly remove role filter for "All Users" tab
      params.role = undefined;
    }

    // Add company filter
    if (companyFilter !== 'all') {
      params.company = companyFilter;
    }

    console.log('Immediate filter params:', params);
    updateQueryParams(params);
  }, [searchTerm, companyFilter, updateQueryParams]);

  // Update search and company filters with debounce
  useEffect(() => {
    debouncedUpdateFilters(searchTerm, selectedTab, companyFilter);
  }, [searchTerm, companyFilter, debouncedUpdateFilters]);

  // Handle role changes immediately (no debounce)
  useEffect(() => {
    applyImmediateRoleFilter(selectedTab);
  }, [selectedTab, applyImmediateRoleFilter]);

  // Show error toast when there are errors (including company fetching errors)
  useEffect(() => {
    if (error && error.includes('Company filter error:')) {
      commandCenterToast.error('Company Filter Error', error.replace('Company filter error: ', ''));
    } else if (error && !error.includes('Company filter error:')) {
      // Only show general errors that aren't company-related to avoid duplicate toasts
      commandCenterToast.error('Data Loading Error', error);
    }
  }, [error, commandCenterToast]);

  // Handler functions for user actions
  const handleCreateUser = async (userData: any): Promise<boolean> => {
    try {
      const success = await createUser(userData);
      if (success) {
        // Use appropriate display name based on user type
        const displayName = userData.userType === 'employer' ? userData.firstName : `${userData.firstName} ${userData.lastName}`;
        commandCenterToast.userCreated(displayName);
        return true;
      } else {
        // Show error toast with specific error message from the hook's error state
        const errorMessage = error || 'Failed to create user. Please try again.';
        commandCenterToast.error('User Creation Failed', errorMessage);
        return false;
      }
    } catch (error) {
      console.error('Error creating user:', error);
      commandCenterToast.error('User Creation Failed', 'An unexpected error occurred while creating the user.');
      return false;
    }
  };

  const handleUpdateUser = async (id: string, userData: any): Promise<boolean> => {
    try {
      const success = await updateUser(id, userData);
      if (success) {
        const user = users.find((u: User) => u.id === id);
        const userName = user ? getUserDisplayName(user) : 'User';
        commandCenterToast.userUpdated(userName);
        return true;
      } else {
        // Show error toast with specific error message from the hook's error state
        const errorMessage = error || 'Failed to update user. Please try again.';
        commandCenterToast.error('User Update Failed', errorMessage);
        return false;
      }
    } catch (error) {
      console.error('Error updating user:', error);
      commandCenterToast.error('User Update Failed', 'An unexpected error occurred while updating the user.');
      return false;
    }
  };

  const handleUserAction = async (action: string, user: User) => {
    const userName = getUserDisplayName(user);

    switch (action) {
      case 'edit':
        setSelectedUser(user);
        setEditUserModalOpen(true);
        break;
      case 'suspend':
        showConfirmation('suspend', userName, user.role, async () => {
          const success = await suspendUser(user.id);
          if (success) {
            commandCenterToast.userSuspended(userName);
          } else {
            commandCenterToast.error('User Suspension Failed', `Failed to suspend ${userName}. Please try again.`);
          }
        });
        break;
      case 'activate':
        showConfirmation('activate', userName, user.role, async () => {
          const success = await activateUser(user.id);
          if (success) {
            commandCenterToast.userUpdated(userName);
          } else {
            commandCenterToast.error('User Activation Failed', `Failed to activate ${userName}. Please try again.`);
          }
        });
        break;
      case 'delete':
        showConfirmation('delete', userName, user.role, async () => {
          const success = await deleteUser(user.id);
          if (success) {
            commandCenterToast.userDeleted(userName);
          } else {
            commandCenterToast.error('User Deletion Failed', `Failed to delete ${userName}. Please try again.`);
          }
        });
        break;
      default:
        break;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'system-admin':
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'employer':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'wellness-coach':
      case 'coach':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'member':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'content-creator':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'suspended':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <CheckCircle className="w-4 h-4" />;
      case 'suspended':
        return <Ban className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      default:
        return null;
    }
  };

  return (
      <div className="p-6 space-y-6 h-screen overflow-y-auto">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        >
          <div>
            <h1 className="text-2xl font-bold text-slate-900 dark:text-slate-100 flex items-center space-x-2">
              <Users className="w-6 h-6" />
              <span>User Management</span>
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              Manage users across all platform services
            </p>
          </div>
          <Button
            onClick={() => {
              console.log('Add User button clicked');
              setCreateUserModalOpen(true);
              console.log('Modal state set to true');
            }}
            className="bg-slate-600 hover:bg-slate-700 dark:bg-slate-700 dark:hover:bg-slate-600"
          >
            <Plus className="w-4 h-4 mr-2" />
            Add User
          </Button>
        </motion.div>



        {/* Search and Filter */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardContent className="p-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <Input
                    placeholder="Search users by name, email, or company..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                <div className="flex flex-col sm:flex-row gap-2">
                  <select
                    value={companyFilter}
                    onChange={(e) => {
                      setCompanyFilter(e.target.value);
                      setPage(1);
                    }}
                    className="px-3 py-2 border border-slate-300 rounded-md bg-white dark:bg-slate-800 dark:border-slate-600 text-sm min-w-[180px]"
                  >
                    <option value="all">All Companies</option>
                    {companies.map((company: string) => (
                      <option key={company} value={company}>{company}</option>
                    ))}
                  </select>
                  <Button variant="outline" className="sm:w-auto">
                    <Building2 className="w-4 h-4 mr-2" />
                    Company Filter
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Filter Info Display (Development Only) */}
        {process.env.NODE_ENV === 'development' && filterInfo && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.15 }}
          >
            <Card className="bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800">
              {/* <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      Filter Endpoint Active
                    </span>
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-300">
                    Role: {filterInfo.appliedRole} → {filterInfo.mappedToUserType}
                  </div>
                </div>
              </CardContent> */}
            </Card>
          </motion.div>
        )}

        {/* User Statistics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              {
                label: 'Total Users',
                value: statistics?.totalUsers?.toLocaleString() || (statistics === null ? 'N/A' : '0'),
                change: statistics ? '+8.2%' : '',
                color: 'text-blue-600'
              },
              {
                label: 'Active Users',
                value: statistics?.activeUsers?.toLocaleString() || (statistics === null ? 'N/A' : '0'),
                change: statistics ? '+5.1%' : '',
                color: 'text-green-600'
              },
              {
                label: 'Suspended',
                value: statistics?.suspendedUsers?.toLocaleString() || (statistics === null ? 'N/A' : '0'),
                change: statistics ? '-2.3%' : '',
                color: 'text-red-600'
              },
              {
                label: 'New This Month',
                value: statistics?.newUsersThisMonth?.toLocaleString() || (statistics === null ? 'N/A' : '0'),
                change: statistics ? '+12.8%' : '',
                color: 'text-purple-600'
              }
            ].map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-slate-600 dark:text-slate-400">
                          {stat.label}
                        </p>
                        <p className="text-2xl font-bold text-slate-900 dark:text-slate-100">
                          {stat.value}
                        </p>
                      </div>
                      {stat.change && (
                        <Badge variant="secondary" className="text-xs">
                          {stat.change}
                        </Badge>
                      )}
                      {statistics === null && (
                        <Badge variant="destructive" className="text-xs">
                          Unavailable
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* User Tabs and List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card className="bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
            <CardHeader>
              <CardTitle>User Directory</CardTitle>
              <CardDescription>
                View and manage all platform users
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs
                value={selectedTab}
                onValueChange={(value) => {
                  console.log('Tab changed from', selectedTab, 'to', value);
                  setSelectedTab(value);
                }}
                className="space-y-6"
              >
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger
                    value="all"
                    disabled={loading}
                    className={loading ? 'opacity-50 cursor-not-allowed' : ''}
                  >
                    All Users
                  </TabsTrigger>
                  <TabsTrigger
                    value="member"
                    disabled={loading}
                    className={loading ? 'opacity-50 cursor-not-allowed' : ''}
                  >
                    Members
                  </TabsTrigger>
                  <TabsTrigger
                    value="employer"
                    disabled={loading}
                    className={loading ? 'opacity-50 cursor-not-allowed' : ''}
                  >
                    Employers
                  </TabsTrigger>
                  <TabsTrigger
                    value="coach"
                    disabled={loading}
                    className={loading ? 'opacity-50 cursor-not-allowed' : ''}
                  >
                    Coaches
                  </TabsTrigger>
                  <TabsTrigger
                    value="admin"
                    disabled={loading}
                    className={loading ? 'opacity-50 cursor-not-allowed' : ''}
                  >
                    Admins
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={selectedTab} className="space-y-4">
                  {loading ? (
                    <div className="text-center py-12">
                      <Loader2 className="w-8 h-8 text-slate-400 mx-auto mb-4 animate-spin" />
                      <p className="text-slate-600 dark:text-slate-400">
                        Loading {selectedTab === 'all' ? 'all users' : `${selectedTab}s`}...
                      </p>
                      {process.env.NODE_ENV === 'development' && (
                        <p className="text-xs text-slate-500 mt-2">
                          Using {selectedTab === 'all' ? 'general' : 'filter'} endpoint
                        </p>
                      )}
                    </div>
                  ) : error ? (
                    <div className="text-center py-12">
                      <AlertCircle className="w-12 h-12 text-red-400 mx-auto mb-4" />
                      <p className="text-red-600 dark:text-red-400 mb-2">
                        Error loading users
                      </p>
                      <p className="text-slate-600 dark:text-slate-400 text-sm">
                        {error}
                      </p>
                      <Button
                        onClick={() => fetchUsers()}
                        variant="outline"
                        className="mt-4"
                      >
                        <RefreshCw className="w-4 h-4 mr-2" />
                        Retry
                      </Button>
                    </div>
                  ) : users.length === 0 ? (
                    <div className="text-center py-12">
                      <Users className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                      <p className="text-slate-600 dark:text-slate-400">
                        No users found matching your criteria
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4 max-h-96 overflow-y-auto pr-2">
                      {users.map((user: User, index: number) => (
                        <motion.div
                          key={user.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                          className="flex items-center justify-between p-4 rounded-lg border border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-700/50 transition-colors"
                        >
                          <div className="flex items-center space-x-4">
                            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold text-sm">
                              {getUserInitials(user)}
                            </div>
                            <div>
                              <div className="flex items-center space-x-2">
                                <h3 className="font-medium text-slate-900 dark:text-slate-100">
                                  {getUserDisplayName(user)}
                                </h3>
                                <Badge className={`text-xs ${getRoleColor(user.role)}`}>
                                  {user.role}
                                </Badge>
                                <Badge className={`text-xs flex items-center space-x-1 ${getStatusColor(user.status)}`}>
                                  {getStatusIcon(user.status)}
                                  <span>{user.status}</span>
                                </Badge>
                              </div>
                              <div className="flex items-center space-x-4 text-sm text-slate-600 dark:text-slate-400 mt-1">
                                <span className="flex items-center space-x-1">
                                  <Mail className="w-3 h-3" />
                                  <span>{user.email}</span>
                                </span>
                                {/* Only show company field for non-employer users to avoid duplication */}
                                {user.role !== 'employer' && user.company && (
                                  <span className="flex items-center space-x-1">
                                    <Building2 className="w-3 h-3" />
                                    <span>{user.company}</span>
                                  </span>
                                )}
                                <span>Last active: {user.lastActive}</span>
                                {user.subscription && (
                                  <span>Plan: {user.subscription}</span>
                                )}
                              </div>
                            </div>
                          </div>
                          
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreVertical className="w-4 h-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleUserAction('edit', user)}>
                                <Edit className="w-4 h-4 mr-2" />
                                Edit User
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleUserAction('suspend', user)}>
                                <Ban className="w-4 h-4 mr-2" />
                                Suspend User
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleUserAction('delete', user)}
                                className="text-red-600 dark:text-red-400"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete User
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </motion.div>
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>
        </motion.div>

        {/* Pagination */}
        {totalPages > 1 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex items-center justify-between"
          >
            <div className="text-sm text-slate-600 dark:text-slate-400">
              Showing {((currentPage - 1) * 10) + 1} to {Math.min(currentPage * 10, total)} of {total} users
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(currentPage - 1)}
                disabled={!hasPrev}
              >
                <ChevronLeft className="w-4 h-4 mr-1" />
                Previous
              </Button>
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  const pageNumber = i + 1;
                  return (
                    <Button
                      key={pageNumber}
                      variant={currentPage === pageNumber ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(pageNumber)}
                      className="w-8 h-8 p-0"
                    >
                      {pageNumber}
                    </Button>
                  );
                })}
                {totalPages > 5 && (
                  <>
                    <span className="text-slate-500">...</span>
                    <Button
                      variant={currentPage === totalPages ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(totalPages)}
                      className="w-8 h-8 p-0"
                    >
                      {totalPages}
                    </Button>
                  </>
                )}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setPage(currentPage + 1)}
                disabled={!hasNext}
              >
                Next
                <ChevronRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </motion.div>
        )}
        
        {/* Modals */}
        <CreateUserModal
          open={createUserModalOpen}
          onOpenChange={setCreateUserModalOpen}
          onCreateUser={handleCreateUser}
          companies={companies}
        />

        <EditUserModal
          open={editUserModalOpen}
          onOpenChange={setEditUserModalOpen}
          onUpdateUser={handleUpdateUser}
          user={selectedUser}
          companies={companies}
        />

        <ConfirmationDialog
          open={dialogState.open}
          onOpenChange={hideConfirmation}
          action={dialogState.action}
          userName={dialogState.userName}
          userRole={dialogState.userRole}
          onConfirm={dialogState.onConfirm}
          isLoading={dialogState.isLoading}
        />
      </div>
  );
}
