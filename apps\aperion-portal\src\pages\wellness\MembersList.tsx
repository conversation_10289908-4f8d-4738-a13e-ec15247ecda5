import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import {
  Search,
  Filter,
  UserPlus,
  MoreHorizontal,
  Calendar,
  MessageSquare,
  FileText,
  Edit,
  Trash2,
  Phone,
  Mail,
  Heart,
  Tag,
  CheckCircle
} from "lucide-react";

export default function MembersList() {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  // Utility functions for styling
  const getHealthScoreColor = (score: number) => {
    if (score >= 80) return "text-green-600 dark:text-green-400";
    if (score >= 60) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  const getProgressColor = (score: number) => {
    if (score >= 80) return "[&>div]:bg-green-500";
    if (score >= 60) return "[&>div]:bg-yellow-500";
    return "[&>div]:bg-red-500";
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800";
      case "attention":
        return "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800";
      case "inactive":
        return "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800";
      case "new":
        return "bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300 dark:border-blue-800";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-800";
    }
  };

  const getRiskLevelBadgeStyle = (riskLevel: string) => {
    switch (riskLevel) {
      case "low":
        return "bg-green-50 text-green-700 border-green-200 dark:bg-green-900/30 dark:text-green-300 dark:border-green-800";
      case "medium":
        return "bg-yellow-50 text-yellow-700 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300 dark:border-yellow-800";
      case "high":
        return "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-800";
      case "unknown":
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-800";
      default:
        return "bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-900/30 dark:text-gray-300 dark:border-gray-800";
    }
  };

  // Mock data for member list
  const members = [
    {
      id: 1,
      name: "Alex Johnson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      healthScore: 82,
      activeGoals: 3,
      lastActivity: "2 hours ago",
      riskLevel: "low",
      tags: ["Fitness", "Nutrition"],
      initials: "AJ",
      imageUrl: null,
    },
    {
      id: 2,
      name: "Maria Rodriguez",
      email: "<EMAIL>",
      phone: "(*************",
      status: "attention",
      healthScore: 58,
      activeGoals: 5,
      lastActivity: "2 days ago",
      riskLevel: "medium",
      tags: ["Stress", "Sleep"],
      initials: "MR",
      imageUrl: null,
    },
    {
      id: 3,
      name: "David Chen",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      healthScore: 91,
      activeGoals: 2,
      lastActivity: "1 hour ago",
      riskLevel: "low",
      tags: ["Cardio", "Mindfulness"],
      initials: "DC",
      imageUrl: null,
    },
    {
      id: 4,
      name: "Sarah Williams",
      email: "<EMAIL>",
      phone: "(*************",
      status: "inactive",
      healthScore: 45,
      activeGoals: 1,
      lastActivity: "1 week ago",
      riskLevel: "high",
      tags: ["Weight Loss"],
      initials: "SW",
      imageUrl: null,
    },
    {
      id: 5,
      name: "Michael Brown",
      email: "<EMAIL>",
      phone: "(*************",
      status: "active",
      healthScore: 76,
      activeGoals: 4,
      lastActivity: "3 hours ago",
      riskLevel: "low",
      tags: ["Strength", "Recovery"],
      initials: "MB",
      imageUrl: null,
    },
    {
      id: 6,
      name: "Emily Davis",
      email: "<EMAIL>",
      phone: "(*************",
      status: "attention",
      healthScore: 63,
      activeGoals: 3,
      lastActivity: "1 day ago",
      riskLevel: "medium",
      tags: ["Nutrition", "Hydration"],
      initials: "ED",
      imageUrl: null,
    },
    {
      id: 7,
      name: "James Wilson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "new",
      healthScore: 0,
      activeGoals: 0,
      lastActivity: "Just joined",
      riskLevel: "unknown",
      tags: ["New Member"],
      initials: "JW",
      imageUrl: null,
    },
    {
      id: 8,
      name: "Lisa Thompson",
      email: "<EMAIL>",
      phone: "(*************",
      status: "new",
      healthScore: 0,
      activeGoals: 1,
      lastActivity: "Just joined",
      riskLevel: "unknown",
      tags: ["New Member", "Onboarding"],
      initials: "LT",
      imageUrl: null,
    }
  ];

  // Filter members based on search query and status
  const filteredMembers = members.filter(member => {
    const matchesSearch = !searchQuery ||
      member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      member.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));

    const matchesStatus = filterStatus === "all" || member.status === filterStatus;

    return matchesSearch && matchesStatus;
  });

  return (
    <div className="p-3 sm:p-4 lg:p-6 space-y-4 lg:space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 sm:gap-4">
        <div>
          <h2 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Members</h2>
          <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400 mt-1">Manage your member profiles and health information</p>
        </div>
        <Button
          onClick={() => navigate("/wellness/members/new")}
          className="shrink-0 w-full sm:w-auto"
          size="sm"
        >
          <UserPlus className="h-4 w-4 mr-2" />
          Add New Member
        </Button>
      </div>


      {/* Members List/Grid */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Member Directory</CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="list" className="w-full">
            {/* Header with tabs and search/filter - responsive layout */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-4 sm:mb-6">
              <TabsList className="grid grid-cols-2 w-fit">
                <TabsTrigger value="list" className="text-xs sm:text-sm px-3 sm:px-4 py-2">List View</TabsTrigger>
                <TabsTrigger value="grid" className="text-xs sm:text-sm px-3 sm:px-4 py-2">Grid View</TabsTrigger>
              </TabsList>

              {/* Search and Filter - responsive positioning */}
              <div className="flex items-center gap-2 sm:gap-3">
                <div className="relative flex-1 sm:flex-none">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search members..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 w-full sm:w-48 lg:w-64"
                  />
                </div>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="icon">
                      <Filter className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-48">
                    <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => setFilterStatus("all")}
                      className={filterStatus === "all" ? "bg-teal-50 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300" : ""}
                    >
                      All Members
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setFilterStatus("active")}
                      className={filterStatus === "active" ? "bg-teal-50 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300" : ""}
                    >
                      Active
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setFilterStatus("attention")}
                      className={filterStatus === "attention" ? "bg-teal-50 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300" : ""}
                    >
                      Needs Attention
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setFilterStatus("inactive")}
                      className={filterStatus === "inactive" ? "bg-teal-50 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300" : ""}
                    >
                      Inactive
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={() => setFilterStatus("new")}
                      className={filterStatus === "new" ? "bg-teal-50 text-teal-700 dark:bg-teal-900/30 dark:text-teal-300" : ""}
                    >
                      New
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            <TabsContent value="list" className="space-y-4">
              {/* Mobile-friendly table with horizontal scroll */}
              <div className="rounded-md border overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="min-w-[200px]">Member</TableHead>
                      <TableHead className="hidden sm:table-cell min-w-[120px]">Health Score</TableHead>
                      <TableHead className="min-w-[100px]">Status</TableHead>
                      <TableHead className="hidden md:table-cell min-w-[80px]">Goals</TableHead>
                      <TableHead className="hidden lg:table-cell min-w-[100px]">Risk Level</TableHead>
                      <TableHead className="hidden lg:table-cell min-w-[120px]">Last Activity</TableHead>
                      <TableHead className="text-right min-w-[80px]">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredMembers.length === 0 ? (
                      <TableRow>
                        <TableCell colSpan={7} className="text-center py-8 text-gray-500 dark:text-gray-400">
                          No members found matching your criteria
                        </TableCell>
                      </TableRow>
                    ) : (
                      filteredMembers.map((member) => (
                        <TableRow
                          key={member.id}
                          className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50"
                          onClick={() => navigate(`/wellness/members/${member.id}`)}
                        >
                          <TableCell className="min-w-[200px]">
                            <div className="flex items-center space-x-3">
                              <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                                {member.imageUrl ? (
                                  <AvatarImage src={member.imageUrl} alt={member.name} />
                                ) : (
                                  <AvatarFallback className="bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300 text-xs sm:text-sm">
                                    {member.initials}
                                  </AvatarFallback>
                                )}
                              </Avatar>
                              <div className="min-w-0 flex-1">
                                <div className="font-medium text-sm sm:text-base text-gray-900 dark:text-white truncate">{member.name}</div>
                                <div className="text-xs text-gray-500 dark:text-gray-400 truncate">{member.email}</div>
                                {/* Show health score on mobile when health score column is hidden */}
                                <div className="sm:hidden mt-1">
                                  <span className={`text-xs font-medium ${getHealthScoreColor(member.healthScore)}`}>
                                    {member.healthScore}% Health Score
                                  </span>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                          <TableCell className="hidden sm:table-cell min-w-[120px]">
                            <div className="flex flex-col">
                              <span className={`text-sm font-medium ${getHealthScoreColor(member.healthScore)}`}>
                                {member.healthScore}%
                              </span>
                              <Progress
                                value={member.healthScore}
                                className={`h-1.5 w-16 mt-1 ${getProgressColor(member.healthScore)}`}
                              />
                            </div>
                          </TableCell>
                          <TableCell className="min-w-[100px]">
                            <Badge variant="outline" className={`text-xs ${getStatusBadgeStyle(member.status)}`}>
                              {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                            </Badge>
                            {/* Show goals on mobile when goals column is hidden */}
                            <div className="md:hidden mt-1 text-xs text-gray-600 dark:text-gray-400">
                              {member.activeGoals} goals
                            </div>
                          </TableCell>
                          <TableCell className="hidden md:table-cell min-w-[80px]">
                            <div className="flex items-center space-x-1">
                              <CheckCircle className="h-4 w-4 text-teal-600 dark:text-teal-400" />
                              <span className="text-sm">{member.activeGoals} active</span>
                            </div>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell min-w-[100px]">
                            <Badge variant="outline" className={`text-xs ${getRiskLevelBadgeStyle(member.riskLevel)}`}>
                              {member.riskLevel.charAt(0).toUpperCase() + member.riskLevel.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell className="hidden lg:table-cell min-w-[120px]">
                            <span className="text-sm text-gray-600 dark:text-gray-400">{member.lastActivity}</span>
                          </TableCell>
                          <TableCell className="text-right min-w-[80px]">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
                                <Button variant="ghost" size="icon" className="h-8 w-8 text-gray-600 dark:text-gray-400">
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/wellness/members/${member.id}`);
                                }}>
                                  <Edit className="mr-2 h-4 w-4" />
                                  View Profile
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/wellness/sessions/new?memberId=${member.id}`);
                                }}>
                                  <Calendar className="mr-2 h-4 w-4" />
                                  Schedule Session
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/wellness/messages?memberId=${member.id}`);
                                }}>
                                  <MessageSquare className="mr-2 h-4 w-4" />
                                  Send Message
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={(e) => {
                                  e.stopPropagation();
                                  navigate(`/wellness/care-plans?memberId=${member.id}`);
                                }}>
                                  <FileText className="mr-2 h-4 w-4" />
                                  Care Plan
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  className="text-red-600 dark:text-red-400"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    // Handle delete
                                  }}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Remove Member
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))
                    )}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>

            <TabsContent value="grid" className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-3 sm:gap-4">
                {filteredMembers.length === 0 ? (
                  <div className="col-span-full text-center py-8 text-gray-500 dark:text-gray-400">
                    No members found matching your criteria
                  </div>
                ) : (
                  filteredMembers.map((member) => (
                    <Card
                      key={member.id}
                      className="cursor-pointer hover:shadow-md transition-shadow"
                      onClick={() => navigate(`/wellness/members/${member.id}`)}
                    >
                      <CardContent className="p-3">
                        <div className="flex items-center justify-between mb-3">
                          <Avatar className="h-10 w-10 sm:h-12 sm:w-12">
                            {member.imageUrl ? (
                              <AvatarImage src={member.imageUrl} alt={member.name} />
                            ) : (
                              <AvatarFallback className="text-xs sm:text-sm bg-teal-100 text-teal-700 dark:bg-teal-900 dark:text-teal-300">
                                {member.initials}
                              </AvatarFallback>
                            )}
                          </Avatar>
                          <Badge variant="outline" className={`text-xs ${getStatusBadgeStyle(member.status)}`}>
                            {member.status.charAt(0).toUpperCase() + member.status.slice(1)}
                          </Badge>
                        </div>

                        <h3 className="font-semibold text-sm sm:text-base text-teal-800 dark:text-teal-200 truncate">{member.name}</h3>
                        <div className="mt-1 space-y-1 text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          <div className="flex items-center min-w-0">
                            <Mail className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-2 text-teal-600 dark:text-teal-400 flex-shrink-0" />
                            <span className="truncate">{member.email}</span>
                          </div>
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 sm:h-3.5 sm:w-3.5 mr-2 text-teal-600 dark:text-teal-400 flex-shrink-0" />
                            <span className="truncate">{member.phone}</span>
                          </div>
                        </div>

                        <div className="flex flex-wrap mt-2 gap-1">
                          {member.tags.slice(0, 1).map((tag, idx) => (
                            <Badge
                              key={idx}
                              variant="secondary"
                              className="flex items-center px-1.5 py-0.5 text-xs bg-teal-50 text-teal-700 border border-teal-200 dark:bg-teal-900/30 dark:text-teal-300 dark:border-teal-800"
                            >
                              <Tag className="h-2.5 w-2.5 mr-1 text-teal-600 dark:text-teal-400" />
                              {tag}
                            </Badge>
                          ))}
                          {member.tags.length > 1 && (
                            <Badge variant="secondary" className="px-1.5 py-0.5 text-xs bg-gray-100 text-gray-600 dark:bg-gray-800 dark:text-gray-400">
                              +{member.tags.length - 1}
                            </Badge>
                          )}
                        </div>

                        <div className="mt-3 pt-3 border-t border-teal-200 dark:border-teal-700">
                          <div className="flex justify-between items-center mb-1">
                            <span className="text-xs text-teal-600 dark:text-teal-400 font-medium">Health Score</span>
                            <span className={`text-xs font-medium ${getHealthScoreColor(member.healthScore)}`}>
                              {member.healthScore}%
                            </span>
                          </div>
                          <Progress value={member.healthScore} className={`h-1.5 ${getProgressColor(member.healthScore)}`} />
                        </div>

                        <div className="mt-2 flex justify-between items-center text-xs text-gray-600 dark:text-gray-400">
                          <div className="flex items-center">
                            <CheckCircle className="h-3 w-3 mr-1 text-teal-600 dark:text-teal-400" />
                            <span>{member.activeGoals} goals</span>
                          </div>
                          <Badge variant="outline" className={`text-xs ${getRiskLevelBadgeStyle(member.riskLevel)}`}>
                            {member.riskLevel}
                          </Badge>
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}