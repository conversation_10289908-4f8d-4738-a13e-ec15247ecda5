import {
  CognitoIdentityProviderClient,
  SignUpCommand,
  AdminCreate<PERSON>serCommand,
  AdminSetUserPasswordCommand,
  AdminDeleteUserCommand,
  AdminConfirmSignUpCommand,
  AdminUpdateUserAttributesCommand,
} from '@aws-sdk/client-cognito-identity-provider';
import { secretHash } from '../../../utils/cryptoUtils';
import { generateRandomPassword } from '../../../utils/passwordUtils';
import * as dotenv from 'dotenv';
import { logger } from '../../../utils/logger';
import { UserRole } from '@aperion/shared';

dotenv.config();

// Initialize Cognito client
const cognitoClient = new CognitoIdentityProviderClient({
  region: process.env.AWS_REGION!,
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
});

// Interface for admin user creation
export interface AdminCreateUserParams {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string | undefined;
  role: UserRole;
  roleId: number;
}

// Interface for Cognito signup response
export interface CognitoSignupResponse {
  userSub: string;
  temporaryPassword: string;
  isTemporary: boolean;
}

/**
 * Create user in AWS Cognito using AdminCreateUser (for admin-created users)
 * This is the preferred method for admin panel user creation
 */
export const createUserInCognito = async (
  userData: AdminCreateUserParams
): Promise<CognitoSignupResponse> => {
  const { firstName, lastName, email, phone, role, roleId } = userData;

  // Determine username (prefer phone number, fallback to email)
  const username = phone || email;

  try {
    logger.info('Creating user in Cognito:', { email, username, role, roleId });

    // Generate a temporary password
    const temporaryPassword = generateRandomPassword();

    // Prepare user attributes (only using attributes that exist in your User Pool)
    const userAttributes = [
      { Name: 'email', Value: email },
      { Name: 'email_verified', Value: 'true' },
      { Name: 'given_name', Value: firstName },
      { Name: 'family_name', Value: lastName },
      { Name: 'custom:roleId', Value: String(roleId) },
      // Note: custom:role, custom:service, custom:permissions removed since they don't exist in your User Pool
    ];

    // Add phone number if provided (using phone_number attribute)
    if (phone) {
      userAttributes.push({ Name: 'phone_number', Value: phone });
    }

    // Create user command - Use phone number as username if available, otherwise email
    const createUserCommand = new AdminCreateUserCommand({
      UserPoolId: process.env.USERPOOLID!,
      Username: username, // Use phone number as username (or email as fallback)
      UserAttributes: userAttributes,
      TemporaryPassword: temporaryPassword,
      MessageAction: 'SUPPRESS', // Suppress welcome email for admin-created users
    });

    const createResponse = await cognitoClient.send(createUserCommand);

    if (!createResponse.User?.Username) {
      throw new Error(
        'Failed to create user - no username returned from Cognito'
      );
    }

    // Extract the actual Cognito User Sub ID from user attributes
    const responseAttributes = createResponse.User.Attributes || [];
    const subAttribute = responseAttributes.find(attr => attr.Name === 'sub');
    const cognitoUserSub = subAttribute?.Value;

    if (!cognitoUserSub) {
      throw new Error('Failed to get Cognito User Sub ID from created user');
    }

    // Set the password as permanent (optional - user can change it later)
    try {
      const setPasswordCommand = new AdminSetUserPasswordCommand({
        UserPoolId: process.env.USERPOOLID!,
        Username: username, // Use same username (phone number or email)
        Password: temporaryPassword,
        Permanent: false, // Keep as temporary so user must change on first login
      });

      await cognitoClient.send(setPasswordCommand);
    } catch (passwordError: any) {
      // If password setting fails, we need to clean up the created user
      logger.error(
        'Failed to set password for created user - initiating cleanup:',
        {
          email,
          username,
          cognitoUserSub,
          error: passwordError.message,
        }
      );

      try {
        await deleteUserFromCognito(username, true);
        logger.info(
          'Successfully cleaned up user after password setting failure:',
          {
            email,
            username,
            cognitoUserSub,
          }
        );
      } catch (cleanupError: any) {
        logger.error(
          'CRITICAL: Failed to cleanup user after password setting failure:',
          {
            email,
            username,
            cognitoUserSub,
            passwordError: passwordError.message,
            cleanupError: cleanupError.message,
          }
        );
      }

      throw new Error(`Failed to set user password: ${passwordError.message}`);
    }

    logger.info('Successfully created user in Cognito:', {
      email,
      username, // Log the actual username used (phone or email)
      cognitoUsername: createResponse.User.Username, // This is the username (phone number)
      cognitoUserSub, // This is the actual User Sub ID (UUID-like)
    });

    return {
      userSub: cognitoUserSub, // Return the actual Cognito User Sub ID, not the username
      temporaryPassword,
      isTemporary: true,
    };
  } catch (error: any) {
    logger.error('Error creating user in Cognito:', {
      email,
      username, // Log the username that was attempted
      error: error.message,
      code: error.name,
    });

    // Handle specific Cognito errors with enhanced error messages
    if (error.name === 'UsernameExistsException') {
      throw new Error(`User with email ${email} already exists in Cognito`);
    } else if (error.name === 'InvalidPasswordException') {
      throw new Error(
        'Generated password does not meet Cognito password policy'
      );
    } else if (error.name === 'InvalidParameterException') {
      // Enhanced error handling for custom attribute issues
      if (
        error.message?.includes('custom:roleId') &&
        error.message.includes('could not be determined')
      ) {
        throw new Error(
          `COGNITO_SCHEMA_ERROR: Attributes did not conform to the schema: Type for attribute {custom:roleId} could not be determined.\n\n` +
            `SOLUTION: Your AWS Cognito User Pool does not have the custom:roleId attribute configured.\n\n` +
            `To fix this issue:\n` +
            `1. Go to AWS Cognito Console\n` +
            `2. Select your User Pool: aperion-user-pool (us-east-1_rDZpEFmxO)\n` +
            `3. Go to "Sign-up experience" → "Attributes"\n` +
            `4. Add custom:roleId attribute (Number type, min: 1, max: 10)\n\n` +
            `Current User Pool has: custom:roleId, phone_number, email`
        );
      } else if (error.message?.includes('custom:')) {
        throw new Error(
          `COGNITO_SCHEMA_ERROR: ${error.message}\n\n` +
            `SOLUTION: This error is related to custom attributes configuration.\n\n` +
            `Your User Pool currently has: custom:roleId, phone_number, email\n` +
            `Make sure you're only using these existing attributes.`
        );
      } else {
        throw new Error(`Invalid user data: ${error.message}`);
      }
    } else if (error.name === 'NotAuthorizedException') {
      throw new Error(
        `COGNITO_AUTH_ERROR: Not authorized to perform this operation.\n\n` +
          `SOLUTION: Check your AWS credentials and permissions:\n` +
          `1. Verify AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY\n` +
          `2. Ensure the IAM user/role has cognito-idp permissions\n` +
          `3. Required permissions: cognito-idp:AdminCreateUser, cognito-idp:AdminSetUserPassword\n\n` +
          `Error details: ${error.message}`
      );
    } else {
      throw new Error(`Failed to create user in Cognito: ${error.message}`);
    }
  }
};

/**
 * Legacy signup function for phone-based registration (kept for backward compatibility)
 */

export const signUpUser = async (userData: {
  phoneNumber: string;
  email: string;
  roleId: number;
}) => {
  const { phoneNumber, email, roleId } = userData;

  try {
    const username = phoneNumber;
    const password = generateRandomPassword();
    const secretHashUser = secretHash(username);

    const command = new SignUpCommand({
      ClientId: process.env.CLIENT_ID!,
      SecretHash: secretHashUser,
      Password: password,
      Username: username,
      UserAttributes: [
        { Name: 'phone_number', Value: phoneNumber }, // Using phone_number attribute
        { Name: 'email', Value: email },
        { Name: 'custom:roleId', Value: String(roleId) },
      ],
    });

    const response = await cognitoClient.send(command);
    return { userSub: response.UserSub!, password };
  } catch (error: any) {
    logger.error('Error in signUpUser:', error);
    throw new Error(`Failed to sign up user: ${error.message}`);
  }
};

/**
 * Delete a user from Cognito
 * @param username The username (email or phone) used to create the user
 * @param isRollback Optional flag to indicate this is a rollback operation for enhanced logging
 */
export const deleteUserFromCognito = async (
  username: string,
  isRollback: boolean = false
): Promise<void> => {
  try {
    logger.info(
      `${isRollback ? 'ROLLBACK: ' : ''}Attempting to delete user from Cognito:`,
      {
        username,
        isRollback,
      }
    );

    const deleteCommand = new AdminDeleteUserCommand({
      UserPoolId: process.env.USERPOOLID!,
      Username: username,
    });

    await cognitoClient.send(deleteCommand);

    logger.info(
      `${isRollback ? 'ROLLBACK: ' : ''}Successfully deleted user from Cognito:`,
      {
        username,
        isRollback,
      }
    );
  } catch (error: any) {
    logger.error(
      `${isRollback ? 'ROLLBACK: ' : ''}Error deleting user from Cognito:`,
      {
        username,
        isRollback,
        error: error.message,
        code: error.name,
      }
    );

    // Enhanced error handling for different scenarios
    if (error.name === 'UserNotFoundException') {
      if (isRollback) {
        // For rollback operations, user not found might be acceptable (already deleted)
        logger.warn(
          'ROLLBACK: User not found in Cognito - may have been already deleted:',
          { username }
        );
        throw new Error(
          `ROLLBACK: User with username ${username} does not exist in Cognito (may have been already deleted)`
        );
      } else {
        throw new Error(
          `User with username ${username} does not exist in Cognito`
        );
      }
    } else if (error.name === 'NotAuthorizedException') {
      throw new Error(
        `COGNITO_AUTH_ERROR: Not authorized to delete user from Cognito.\n\n` +
          `SOLUTION: Check your AWS credentials and permissions:\n` +
          `1. Verify AWS_ACCESS_KEY_ID and AWS_SECRET_ACCESS_KEY\n` +
          `2. Ensure the IAM user/role has cognito-idp:AdminDeleteUser permission\n\n` +
          `Error details: ${error.message}`
      );
    } else {
      throw new Error(`Failed to delete user from Cognito: ${error.message}`);
    }
  }
};

//cognito Account Confirmation
interface ConfirmAccountResponse {
  status: number;
  message: string;
  data?: unknown;
  error?: string;
}

export const confirmAccount = async (
  username: string | undefined
): Promise<ConfirmAccountResponse> => {
  if (!username) {
    return {
      status: 2,
      message: 'Username parameter is required',
    };
  }


  console.log(username, "username");
  
  try {
    // Update user attributes to mark email/phone as verified
    await cognitoClient.send(
      new AdminUpdateUserAttributesCommand({
        UserPoolId: process.env.USERPOOLID! || 'us-east-1_rDZpEFmxO',
        // UserPoolId: process.env.USERPOOLID!,
        Username: username,
        UserAttributes: [
          { Name: 'email_verified', Value: 'true' },
          { Name: 'phone_number_verified', Value: 'true' },
        ],
      })
    );

    // Confirm the user account
    const confirmCommand = new AdminConfirmSignUpCommand({
      UserPoolId: process.env.USERPOOLID!,
      Username: username,
    });

    const response = await cognitoClient.send(confirmCommand);

    return {
      status: 1,
      message: 'Account confirmed successfully',
      data: response,
    };
  } catch (error: any) {
    console.error('Error in confirmAccount:', error);

    // Handle specific Cognito errors with enhanced error messages

    if (error.name === 'UserNotFoundException') {
      return {
        status: 2,
        message: 'User not found',
        error: error.message,
      };
    }

   if (error.name === 'NotAuthorizedException') {
  if (error.message === 'User is already confirmed') {
    return {
      status: 1,
      message: 'Account is already confirmed',
    };
  }

  return {
    status: 2,
    message: 'Invalid request',
    error: error.message,
  };
}


    return {
      status: 0,
      message: 'Failed to confirm account',
      error: error.message,
    };
  }
};
