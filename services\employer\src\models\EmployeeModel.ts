import { Pool } from 'pg';
import { logger } from '../utils/logger';
import { CreateEmployeeRequest } from '../types/employee';
import { config } from '../config';

// Force cache invalidation - updated at 2025-06-10T09:47:00Z

export interface EmployeeRecord {
  id: string;
  cognitoUserId: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  userType: string;
  serviceUserId: string;
  status: string;
  createdAt: Date;
  updatedAt: Date;
}

export class EmployeeModel {
  private pool: Pool;

  constructor() {
    // Initialize database connection using local config
    this.pool = new Pool({
      host: config.database.host,
      port: config.database.port,
      database: config.database.name,
      user: config.database.user,
      password: config.database.password,
      ssl: config.database.ssl,
      max: config.database.connectionLimit,
      idleTimeoutMillis: config.database.idleTimeout,
      connectionTimeoutMillis: config.database.connectionTimeout,
    });

    // Handle pool errors
    this.pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
  }

  /**
   * Execute a query with automatic client management
   */
  async query(text: string, params?: any[]): Promise<any> {
    const client = await this.pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  }
  /**
   * Check if employee exists by email
   */
  async getEmployeesByEmail(email: string): Promise<EmployeeRecord[]> {
    const query = `
      SELECT
        id,
        cognito_user_id as "cognitoUserId",
        first_name as "firstName",
        last_name as "lastName",
        email,
        phone_number as "phone",
        user_type as "userType",
        service_user_id as "serviceUserId",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
      FROM shared_data.user_references
      WHERE email = $1 AND status != 'deleted'
      LIMIT 1
    `;

    try {
      const result = await this.query(query, [email]);
      return result.rows;
    } catch (error) {
      logger.error('Error checking employee existence:', error);
      throw new Error('Failed to check employee existence');
    }
  }

  /**
   * Create employee in shared_data.user_references (following Command Center pattern)
   */
  async createEmployee(employeeData: CreateEmployeeRequest & {
    cognitoUserId: string;
    userType: string;
  }): Promise<EmployeeRecord> {
    const query = `
      INSERT INTO shared_data.user_references (
        cognito_user_id,
        user_type,
        service_user_id,
        email,
        phone_number,
        first_name,
        last_name,
        status
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING
        id,
        cognito_user_id as "cognitoUserId",
        first_name as "firstName",
        last_name as "lastName",
        email,
        phone_number as "phone",
        user_type as "userType",
        service_user_id as "serviceUserId",
        status,
        created_at as "createdAt",
        updated_at as "updatedAt"
    `;

    // Generate service_user_id (following Command Center pattern)
    const serviceUserIdQuery = 'SELECT gen_random_uuid() as uuid';
    const serviceUserIdResult = await this.query(serviceUserIdQuery);
    const serviceUserId = serviceUserIdResult.rows[0].uuid;

    logger.info('Creating employee in database:', {
      email: employeeData.email,
      cognitoUserId: employeeData.cognitoUserId,
      userType: employeeData.userType,
    });

    try {
      const result = await this.query(query, [
        employeeData.cognitoUserId,
        employeeData.userType,        // Must be 'member'
        serviceUserId,
        employeeData.email,
        employeeData.phoneNumber,
        employeeData.firstName,
        employeeData.lastName,
        'active'
      ]);

      return result.rows[0];
    } catch (error) {
      logger.error('Error creating employee:', error);
      throw new Error('Failed to create employee');
    }
  }
}
