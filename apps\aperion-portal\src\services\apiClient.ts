import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * Centralized API client with automatic authentication token injection
 */
class ApiClient {
  private client: AxiosInstance;

  constructor() {
    this.client = axios.create({
      baseURL: '/api',
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Request interceptor to add authentication token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getStoredToken();

        if (token) {
          // Check if token is expired before making request
          if (!this.hasValidToken()) {
            console.warn('⚠️ Token is expired, clearing stored tokens');
            this.clearStoredToken();

            // Redirect to login if not already on auth page
            if (!window.location.pathname.startsWith('/auth/')) {
              window.location.href = '/auth/send-code';
              return Promise.reject(new Error('Token expired'));
            }
          } else {
            config.headers.Authorization = `Bearer ${token}`;

            // Only log in development mode
            if (process.env.NODE_ENV === 'development') {
              console.log('🔐 API Request:', {
                url: config.url,
                method: config.method?.toUpperCase(),
                hasToken: true,
                tokenExpiry: this.getTokenExpiration()?.toISOString()
              });
            }
          }
        } else {
          // Only warn for non-auth endpoints
          if (!config.url?.includes('/auth/')) {
            console.warn('⚠️ No authentication token found for API request:', config.url);
          }
        }

        return config;
      },
      (error) => {
        console.error('❌ Request interceptor error:', error);
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle authentication errors
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          console.error('🔒 Authentication failed:', {
            url: error.config?.url,
            method: error.config?.method,
            message: error.response?.data?.error?.message || 'Unauthorized',
            timestamp: new Date().toISOString()
          });

          // Clear invalid tokens and redirect to login
          this.clearStoredToken();
          console.warn('🚨 Redirecting to login due to authentication failure');

          // Prevent infinite redirect loops by checking current location
          if (!window.location.pathname.startsWith('/auth/')) {
            window.location.href = '/auth/send-code';
          }
        }
        return Promise.reject(error);
      }
    );
  }

  /**
   * Get the stored authentication token
   */
  private getStoredToken(): string | null {
    return localStorage.getItem('aperion_token');
  }

  /**
   * Check if token exists and is not expired
   */
  public hasValidToken(): boolean {
    const token = this.getStoredToken();
    if (!token) return false;

    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) return false;

      const now = Math.floor(Date.now() / 1000);
      return decoded.exp > now;
    } catch (error) {
      console.error('Error checking token validity:', error);
      return false;
    }
  }

  /**
   * Get token expiration time
   */
  public getTokenExpiration(): Date | null {
    const token = this.getStoredToken();
    if (!token) return null;

    try {
      const decoded = this.decodeToken(token);
      if (!decoded || !decoded.exp) return null;
      return new Date(decoded.exp * 1000);
    } catch (error) {
      console.error('Error getting token expiration:', error);
      return null;
    }
  }

  /**
   * Decode JWT token payload (for debugging)
   */
  private decodeToken(token: string): any {
    try {
      const payload = token.split('.')[1];
      const decoded = JSON.parse(atob(payload));
      return decoded;
    } catch (error) {
      console.error('Failed to decode token:', error);
      return null;
    }
  }

  /**
   * Validate token and log details (for debugging)
   */
  public validateStoredToken(): void {
    const token = this.getStoredToken();

    if (!token) {
      console.warn('🔑 No token found in localStorage');
      return;
    }

    const decoded = this.decodeToken(token);
    if (!decoded) {
      console.error('🔑 Invalid token format');
      return;
    }

    const now = Math.floor(Date.now() / 1000);
    const isExpired = decoded.exp && decoded.exp < now;

    console.log('🔑 Token Validation:', {
      hasToken: true,
      tokenLength: token.length,
      isExpired,
      expiresAt: decoded.exp ? new Date(decoded.exp * 1000).toISOString() : 'No expiration',
      currentTime: new Date().toISOString(),
      payload: {
        sub: decoded.sub,
        email: decoded.email,
        role: decoded['custom:role'],
        service: decoded['custom:service'],
        iss: decoded.iss,
        aud: decoded.aud
      }
    });
  }

  /**
   * Clear the stored authentication token
   */
  private clearStoredToken(): void {
    localStorage.removeItem('aperion_token');
    localStorage.removeItem('aperion_user');
  }

  /**
   * Make a GET request
   */
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.get<T>(url, config);
  }

  /**
   * Make a POST request
   */
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.post<T>(url, data, config);
  }

  /**
   * Make a PUT request
   */
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.put<T>(url, data, config);
  }

  /**
   * Make a DELETE request
   */
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.delete<T>(url, config);
  }

  /**
   * Make a PATCH request
   */
  async patch<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.patch<T>(url, data, config);
  }

  /**
   * Get authentication headers for manual requests
   * @deprecated Use the apiClient methods instead for automatic token handling
   */
  public getAuthHeaders(): Record<string, string> {
    const token = this.getStoredToken();
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (token && this.hasValidToken()) {
      headers.Authorization = `Bearer ${token}`;
    }

    return headers;
  }

  /**
   * Get the underlying axios instance for advanced usage
   */
  getInstance(): AxiosInstance {
    return this.client;
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();

// Add debug functions to window object for browser console access
if (typeof window !== 'undefined') {
  (window as any).debugAuth = {
    validateToken: () => apiClient.validateStoredToken(),
    getToken: () => localStorage.getItem('aperion_token'),
    getUser: () => localStorage.getItem('aperion_user'),
    clearTokens: () => {
      localStorage.removeItem('aperion_token');
      localStorage.removeItem('aperion_user');
      console.log('🧹 Tokens cleared');
    },
    testUserManagementAPI: async () => {
      try {
        console.log('🧪 Testing User Management API...');
        const response = await apiClient.get('/command-center/admin/users/dev', {
          params: { limit: 1 }
        });
        console.log('✅ API Test Success:', response.data);
        return response.data;
      } catch (error) {
        console.error('❌ API Test Failed:', error);
        return error;
      }
    }
  };

  console.log('🔧 Debug functions available: window.debugAuth');
  console.log('   - validateToken(): Check current token');
  console.log('   - getToken(): Get stored token');
  console.log('   - getUser(): Get stored user');
  console.log('   - clearTokens(): Clear all tokens');
  console.log('   - testUserManagementAPI(): Test API call');
}

export default apiClient;
